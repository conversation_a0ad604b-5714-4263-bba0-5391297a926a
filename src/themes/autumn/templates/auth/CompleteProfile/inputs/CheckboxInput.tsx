import { twMerge } from 'tailwind-merge';

interface FieldCheckboxInputProps {
  checkboxClassName?: string;
  checked: boolean;
  customText?: string;
  labelClassName?: string;
  setField: (value: boolean) => void;
  text: string;
}

export default function FieldCheckboxInput({
  checkboxClassName,
  checked,
  customText,
  labelClassName,
  setField,
  text,
}: FieldCheckboxInputProps) {
  return (
    <label
      className={twMerge(
        'flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800',
        labelClassName,
      )}
      htmlFor={text}
    >
      <input
        checked={checked}
        className={twMerge(
          'm-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600',
          checkboxClassName,
        )}
        id={text}
        name={text}
        onChange={(e) => {
          setField(e.currentTarget.checked);
        }}
        type="checkbox"
      />

      {customText ?? text}
    </label>
  );
}
