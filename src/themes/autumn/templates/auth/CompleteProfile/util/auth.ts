import {
  AuthField,
  attemptTokenLogin,
  authErrorToast,
  createPassword,
  generateToken,
  handleErrors,
  userUpdate,
} from 'util/auth';

import type { PhoenixApiError } from 'types/phoenix-types/responses';

interface CreatePasswordParams {
  accessToken: string;
  password: string;
  setErrors: (errors: PhoenixApiError[]) => void;
}

async function doCreatePassword({
  accessToken,
  password,
  setErrors,
}: CreatePasswordParams): Promise<boolean> {
  if (!accessToken) {
    return false;
  }

  const createPasswordRes = await createPassword({
    accessToken,
    password,
  });

  if (createPasswordRes.success) {
    return true;
  }

  handleErrors({
    errors: createPasswordRes.errors,
    setErrors,
  });
  return false;
}

interface SubmitProfileParams {
  accessToken: string;
  agsPropertyProduce?: string;
  canSetPassword: boolean;
  fields: Record<AuthField, string>;
  isBillingSameAsDelivery: boolean;
  password: string;
  setErrors: (errors: Phoenix<PERSON>piError[]) => void;
}

export async function submitProfile({
  accessToken,
  agsPropertyProduce,
  canSetPassword,
  fields,
  isBillingSameAsDelivery,
  password,
  setErrors,
}: SubmitProfileParams): Promise<boolean> {
  const updateRes = await userUpdate({
    accessToken,
    agsPropertyProduce,
    alpaBusiness: fields[AuthField.ALPA_BUSINESS] || undefined,
    anonymousSupporter: ['true', 'false'].includes(
      fields[AuthField.ANONYMOUS_SUPPORTER],
    )
      ? fields[AuthField.ANONYMOUS_SUPPORTER] === 'true'
      : undefined,
    billingAddress:
      fields[
        isBillingSameAsDelivery
          ? AuthField.DELIVERY_ADDRESS
          : AuthField.BILLING_ADDRESS
      ],
    billingPostcode:
      fields[
        isBillingSameAsDelivery
          ? AuthField.DELIVERY_POSTCODE
          : AuthField.BILLING_POSTCODE
      ],
    billingState:
      fields[
        isBillingSameAsDelivery
          ? AuthField.DELIVERY_STATE
          : AuthField.BILLING_STATE
      ],
    billingSuburb:
      fields[
        isBillingSameAsDelivery
          ? AuthField.DELIVERY_SUBURB
          : AuthField.BILLING_SUBURB
      ],
    contactNumber: fields[AuthField.CONTACT_NUMBER],
    deliveryAddress: fields[AuthField.DELIVERY_ADDRESS],
    deliveryPostcode: fields[AuthField.DELIVERY_POSTCODE],
    deliveryState: fields[AuthField.DELIVERY_STATE],
    deliverySuburb: fields[AuthField.DELIVERY_SUBURB],
    firstName: fields[AuthField.FIRST_NAME],
    isBillingSameAsDelivery,
    lastName: fields[AuthField.LAST_NAME],
  });

  if (!updateRes.success) {
    handleErrors({
      errors: updateRes.errors,
      setErrors,
    });
    return false;
  }

  if (!canSetPassword || password.trim() === '') {
    return true;
  }

  const passwordSetSuccesfully = await doCreatePassword({
    accessToken,
    password,
    setErrors,
  });

  return passwordSetSuccesfully;
}

export interface SubmitEnrichmentsFields {
  farmingInvolvement?: string[];
  farmingSectors?: string[];
  regionalBirthYear?: string;
  regionalConnections?: string[];
}

export interface SubmitEnrichmentParams extends SubmitEnrichmentsFields {
  accessToken: string;
  setErrors: (errors: PhoenixApiError[]) => void;
}

export async function submitEnrichments({
  accessToken,
  farmingInvolvement,
  farmingSectors,
  regionalBirthYear,
  regionalConnections,
  setErrors,
}: SubmitEnrichmentParams): Promise<boolean> {
  const updateRes = await userUpdate({
    accessToken,
    farmingInvolvement,
    farmingSectors,
    regionalBirthYear,
    regionalConnections,
  });

  if (!updateRes.success) {
    handleErrors({
      errors: updateRes.errors,
      setErrors,
    });
  }

  return updateRes.success;
}

export async function refreshTokenAndRedirect(
  accessToken: string,
): Promise<boolean> {
  try {
    const generateTokenRes = await generateToken({ accessToken });
    if (generateTokenRes.success) {
      const { accessToken: newAccessToken } = generateTokenRes.data;
      try {
        await attemptTokenLogin(newAccessToken);
        return true;
      } catch (e) {
        console.error(e);
        return false;
      }
    } else {
      return false;
    }
  } catch {
    authErrorToast('Unable to connect to authentication server');
    return false;
  }
}
