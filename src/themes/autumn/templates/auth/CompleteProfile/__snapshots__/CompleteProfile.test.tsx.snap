// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`<CompleteProfile /> renders with ALPA 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="h-0 w-full"
  />
  <div
    class="top-0 z-30 w-full bg-white px-6 md:shadow-md sticky shadow-md"
  >
    <div
      class="relative mx-auto flex h-16 w-full max-w-container items-center justify-between"
    >
      <div
        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
      >
        <div
          class="h-9 w-56"
          data-testid="logo"
        >
          <img
            alt=""
            class="size-full max-h-full max-w-full object-contain"
          />
        </div>
      </div>
      <div
        class="hidden flex-row md:flex"
      />
    </div>
  </div>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="flex min-h-[calc(100vh-64px)] flex-col justify-between"
    >
      <div
        class="flex justify-center border-t border-gray-300 px-4 transition-opacity"
      >
        <div
          class="relative w-full max-w-375 px-4 font-inter text-gray-900"
        >
          <div
            class="w-full transition-opacity duration-500 ease-in-out data-[leave]:absolute data-[closed]:opacity-0"
          >
            <div
              class="w-full md:max-w-xs"
            >
              <div
                class="text-xl font-semibold md:text-2xl mt-7"
              >
                Complete profile
              </div>
              <form
                class="w-full"
              >
                <div
                  class="mt-5 flex flex-col gap-y-3"
                >
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        Email
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border border-gray-400 py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-400 opacity-0"
                        disabled=""
                        name="email"
                        placeholder=""
                      />
                    </div>
                  </div>
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        First Name
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-500 border-gray-900 pr-5 opacity-0"
                        disabled=""
                        name="first_name"
                        type="text"
                        value=""
                      />
                    </div>
                  </div>
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        Last Name
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-500 border-gray-900 pr-5 opacity-0"
                        disabled=""
                        name="last_name"
                        type="text"
                        value=""
                      />
                    </div>
                  </div>
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        Contact Number
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-500 border-gray-900 pr-5 opacity-0"
                        disabled=""
                        name="contact_number"
                        type="tel"
                        value=""
                      />
                    </div>
                  </div>
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        Postcode
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-500 border-gray-900 pr-5 opacity-0"
                        disabled=""
                        maxlength="4"
                        name="delivery_postcode"
                        type="text"
                        value=""
                      />
                    </div>
                  </div>
                </div>
                <div
                  class="flex md:flex-row md:justify-end lg:flex-row lg:justify-end md:w-full w-full mb-8 mt-5 opacity-0"
                >
                  <button
                    class="flex items-center justify-center rounded-md bg-red-600 disabled:bg-gray-300 h-10.5 md:h-10.5 lg:h-10.5 hover:bg-red-700 disabled:hover:bg-gray-300 md:w-full w-full"
                    disabled=""
                    type="submit"
                  >
                    <span
                      class="py-2 font-medium leading-6 text-sm px-4 text-white"
                    >
                      Continue
                    </span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  />
  <div
    class="fixed bottom-20 left-1/2 right-auto z-50 mx-auto flex max-h-56 w-full max-w-[310px] -translate-x-2/4 items-center justify-center overflow-hidden rounded-lg md:max-w-[510px] md:bottom-4"
    id="popup-container"
  />
</div>
`;

exports[`<CompleteProfile /> renders with Ags enrichments 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="h-0 w-full"
  />
  <div
    class="top-0 z-30 w-full bg-white px-6 md:shadow-md sticky shadow-md"
  >
    <div
      class="relative mx-auto flex h-16 w-full max-w-container items-center justify-between"
    >
      <div
        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
      >
        <div
          class="h-9 w-56"
          data-testid="logo"
        >
          <img
            alt=""
            class="size-full max-h-full max-w-full object-contain"
          />
        </div>
      </div>
      <div
        class="hidden flex-row md:flex"
      />
    </div>
  </div>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="flex min-h-[calc(100vh-64px)] flex-col justify-between"
    >
      <div
        class="flex justify-center border-t border-gray-300 px-4 transition-opacity"
      >
        <div
          class="relative w-full max-w-375 px-4 font-inter text-gray-900"
        >
          <div
            class="w-full transition-opacity duration-500 ease-in-out data-[leave]:absolute data-[closed]:opacity-0"
          >
            <div
              class="w-full md:max-w-xs"
            >
              <div
                class="mt-6 text-sm leading-6 text-gray-600"
              >
                Step 1/
                2
              </div>
              <div
                class="text-xl font-semibold md:text-2xl mt-2"
              >
                Complete profile
              </div>
              <form
                class="w-full"
              >
                <div
                  class="mt-5 flex flex-col gap-y-3"
                >
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        Email
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border border-gray-400 py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-400 opacity-0"
                        disabled=""
                        name="email"
                        placeholder=""
                      />
                    </div>
                  </div>
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        First Name
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-500 border-gray-900 pr-5 opacity-0"
                        disabled=""
                        name="first_name"
                        type="text"
                        value=""
                      />
                    </div>
                  </div>
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        Last Name
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-500 border-gray-900 pr-5 opacity-0"
                        disabled=""
                        name="last_name"
                        type="text"
                        value=""
                      />
                    </div>
                  </div>
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        Contact Number
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-500 border-gray-900 pr-5 opacity-0"
                        disabled=""
                        name="contact_number"
                        type="tel"
                        value=""
                      />
                    </div>
                  </div>
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        Postcode
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-500 border-gray-900 pr-5 opacity-0"
                        disabled=""
                        maxlength="4"
                        name="delivery_postcode"
                        type="text"
                        value=""
                      />
                    </div>
                  </div>
                </div>
                <div
                  class="flex md:flex-row md:justify-end lg:flex-row lg:justify-end md:w-full w-full mb-8 mt-5 opacity-0"
                >
                  <button
                    class="flex items-center justify-center rounded-md bg-red-600 disabled:bg-gray-300 h-10.5 md:h-10.5 lg:h-10.5 hover:bg-red-700 disabled:hover:bg-gray-300 md:w-full w-full"
                    disabled=""
                    type="submit"
                  >
                    <span
                      class="py-2 font-medium leading-6 text-sm px-4 text-white"
                    >
                      Continue
                    </span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  />
  <div
    class="fixed bottom-20 left-1/2 right-auto z-50 mx-auto flex max-h-56 w-full max-w-[310px] -translate-x-2/4 items-center justify-center overflow-hidden rounded-lg md:max-w-[510px] md:bottom-4"
    id="popup-container"
  />
</div>
`;

exports[`<CompleteProfile /> renders with no enrichments 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="h-0 w-full"
  />
  <div
    class="top-0 z-30 w-full bg-white px-6 md:shadow-md sticky shadow-md"
  >
    <div
      class="relative mx-auto flex h-16 w-full max-w-container items-center justify-between"
    >
      <div
        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
      >
        <div
          class="h-9 w-56"
          data-testid="logo"
        >
          <img
            alt=""
            class="size-full max-h-full max-w-full object-contain"
          />
        </div>
      </div>
      <div
        class="hidden flex-row md:flex"
      />
    </div>
  </div>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="flex min-h-[calc(100vh-64px)] flex-col justify-between"
    >
      <div
        class="flex justify-center border-t border-gray-300 px-4 transition-opacity"
      >
        <div
          class="relative w-full max-w-375 px-4 font-inter text-gray-900"
        >
          <div
            class="w-full transition-opacity duration-500 ease-in-out data-[leave]:absolute data-[closed]:opacity-0"
          >
            <div
              class="w-full md:max-w-xs"
            >
              <div
                class="text-xl font-semibold md:text-2xl mt-7"
              >
                Complete profile
              </div>
              <form
                class="w-full"
              >
                <div
                  class="mt-5 flex flex-col gap-y-3"
                >
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        Email
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border border-gray-400 py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-400 opacity-0"
                        disabled=""
                        name="email"
                        placeholder=""
                      />
                    </div>
                  </div>
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        First Name
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-500 border-gray-900 pr-5 opacity-0"
                        disabled=""
                        name="first_name"
                        type="text"
                        value=""
                      />
                    </div>
                  </div>
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        Last Name
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-500 border-gray-900 pr-5 opacity-0"
                        disabled=""
                        name="last_name"
                        type="text"
                        value=""
                      />
                    </div>
                  </div>
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        Contact Number
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-500 border-gray-900 pr-5 opacity-0"
                        disabled=""
                        name="contact_number"
                        type="tel"
                        value=""
                      />
                    </div>
                  </div>
                  <div
                    class="pointer-events-none select-none"
                  >
                    <div
                      class="relative inline-block text-sm font-semibold leading-3"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <span
                        class="opacity-0"
                      >
                        Postcode
                      </span>
                    </div>
                    <div
                      class="relative mt-2"
                    >
                      <div
                        class="absolute size-full bg-white"
                      >
                        <div
                          class="size-full animate-pulse bg-gray-300"
                        />
                      </div>
                      <input
                        class="w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-4 placeholder:leading-4 placeholder:text-gray-500 border-gray-900 pr-5 opacity-0"
                        disabled=""
                        maxlength="4"
                        name="delivery_postcode"
                        type="text"
                        value=""
                      />
                    </div>
                  </div>
                </div>
                <div
                  class="flex md:flex-row md:justify-end lg:flex-row lg:justify-end md:w-full w-full mb-8 mt-5 opacity-0"
                >
                  <button
                    class="flex items-center justify-center rounded-md bg-red-600 disabled:bg-gray-300 h-10.5 md:h-10.5 lg:h-10.5 hover:bg-red-700 disabled:hover:bg-gray-300 md:w-full w-full"
                    disabled=""
                    type="submit"
                  >
                    <span
                      class="py-2 font-medium leading-6 text-sm px-4 text-white"
                    >
                      Continue
                    </span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  />
  <div
    class="fixed bottom-20 left-1/2 right-auto z-50 mx-auto flex max-h-56 w-full max-w-[310px] -translate-x-2/4 items-center justify-center overflow-hidden rounded-lg md:max-w-[510px] md:bottom-4"
    id="popup-container"
  />
</div>
`;

exports[`<EnrichmentForm /> Renders with Ags enrichments 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="mt-6 text-sm leading-6 text-gray-600"
  >
    Step 2/
    3
  </div>
  <div
    class="mt-3 text-xl font-semibold md:text-2xl"
  >
    Final step to improve your experience
  </div>
  <form
    class="mt-5 flex w-full flex-col gap-y-4"
  >
    <div
      class="pb-1 text-sm font-semibold leading-5 text-gray-900"
    >
      Which of the following best describes your involvement in farming (tick all boxes that apply):
    </div>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="I own a farm"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="I own a farm"
        name="I own a farm"
        type="checkbox"
      />
      I own a farm
    </label>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="I manage a farm"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="I manage a farm"
        name="I manage a farm"
        type="checkbox"
      />
      I manage a farm
    </label>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="I work on a farm"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="I work on a farm"
        name="I work on a farm"
        type="checkbox"
      />
      I work on a farm
    </label>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="I have a hobby farm or lifestyle property"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="I have a hobby farm or lifestyle property"
        name="I have a hobby farm or lifestyle property"
        type="checkbox"
      />
      I have a hobby farm or lifestyle property
    </label>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="My family are farmers"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="My family are farmers"
        name="My family are farmers"
        type="checkbox"
      />
      My family are farmers
    </label>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="I supply farm equipment or products"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="I supply farm equipment or products"
        name="I supply farm equipment or products"
        type="checkbox"
      />
      I supply farm equipment or products
    </label>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="I provide consulting or support services to farms"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="I provide consulting or support services to farms"
        name="I provide consulting or support services to farms"
        type="checkbox"
      />
      I provide consulting or support services to farms
    </label>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="None of the above, I'm not involved with farming"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="None of the above, I'm not involved with farming"
        name="None of the above, I'm not involved with farming"
        type="checkbox"
      />
      None of the above, I'm not involved with farming
    </label>
    <div
      class="flex md:flex-row md:justify-end lg:flex-row lg:justify-end md:w-full w-full mb-8 mt-6"
    >
      <button
        class="flex items-center justify-center rounded-md bg-red-600 disabled:bg-gray-300 h-10.5 md:h-10.5 lg:h-10.5 hover:bg-red-700 disabled:hover:bg-gray-300 md:w-full w-full"
        type="submit"
      >
        <span
          class="py-2 font-medium leading-6 text-sm px-4 text-white"
        >
          Finish
        </span>
      </button>
    </div>
  </form>
</div>
`;

exports[`<EnrichmentForm /> Renders with Ags enrichments and ALPA 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="mt-6 text-sm leading-6 text-gray-600"
  >
    Step 2/
    3
  </div>
  <div
    class="mt-3 text-xl font-semibold md:text-2xl"
  >
    Final step to improve your experience
  </div>
  <form
    class="mt-5 flex w-full flex-col gap-y-4"
  >
    <div
      class="pb-1 text-sm font-semibold leading-5 text-gray-900"
    >
      Which of the following best describes your involvement in farming (tick all boxes that apply):
    </div>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="I own a farm"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="I own a farm"
        name="I own a farm"
        type="checkbox"
      />
      I own a farm
    </label>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="I manage a farm"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="I manage a farm"
        name="I manage a farm"
        type="checkbox"
      />
      I manage a farm
    </label>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="I work on a farm"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="I work on a farm"
        name="I work on a farm"
        type="checkbox"
      />
      I work on a farm
    </label>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="I have a hobby farm or lifestyle property"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="I have a hobby farm or lifestyle property"
        name="I have a hobby farm or lifestyle property"
        type="checkbox"
      />
      I have a hobby farm or lifestyle property
    </label>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="My family are farmers"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="My family are farmers"
        name="My family are farmers"
        type="checkbox"
      />
      My family are farmers
    </label>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="I supply farm equipment or products"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="I supply farm equipment or products"
        name="I supply farm equipment or products"
        type="checkbox"
      />
      I supply farm equipment or products
    </label>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="I provide consulting or support services to farms"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="I provide consulting or support services to farms"
        name="I provide consulting or support services to farms"
        type="checkbox"
      />
      I provide consulting or support services to farms
    </label>
    <label
      class="flex cursor-pointer select-none flex-row gap-x-2 text-sm leading-5 text-gray-800"
      for="None of the above, I'm not involved with farming"
    >
      <input
        class="m-0 mt-0.5 size-4.5 cursor-pointer appearance-none rounded border-2 border-black/25 bg-white checked:border-0 checked:bg-green-600 checked:text-green-600"
        id="None of the above, I'm not involved with farming"
        name="None of the above, I'm not involved with farming"
        type="checkbox"
      />
      None of the above, I'm not involved with farming
    </label>
    <div
      class="flex md:flex-row md:justify-end lg:flex-row lg:justify-end md:w-full w-full mb-8 mt-6"
    >
      <button
        class="flex items-center justify-center rounded-md bg-red-600 disabled:bg-gray-300 h-10.5 md:h-10.5 lg:h-10.5 hover:bg-red-700 disabled:hover:bg-gray-300 md:w-full w-full"
        type="submit"
      >
        <span
          class="py-2 font-medium leading-6 text-sm px-4 text-white"
        >
          Finish
        </span>
      </button>
    </div>
  </form>
</div>
`;
