'use client';

import clsx from 'clsx';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import TagManager from 'react-gtm-module';

import {
  Ga4EventType,
  ImpressionType,
  InteractionType,
} from 'components/GoogleTagManager/enums';
import { onPianoReady } from 'components/Piano/ready';
import { useAppSelector } from 'store/hooks';
import { ThemeVariant } from 'store/slices/conf';
import {
  PaywallCampaignPillPosition,
  PaywallCampaignTermDisplay,
} from 'store/slices/features';
import { SimplePeriodUnit, SimpleResourceType } from 'types/Piano';
import { AbExperiment, useAbExperiment } from 'util/ab-tests';
import { redirectToLogin, redirectToRegister } from 'util/auth';
import {
  createEcommerceItem,
  createImpressionItem,
  sendAbTestImpression,
  sendAddToCartGa4,
  sendBeginCheckout,
  sendToGtm,
  setGa4GtmCommerce,
  setGtmDataLayer,
} from 'util/gtm';
import { formatPrice, formatSavingsPercentage } from 'util/piano/format';
import { calculatePeriodRatio } from 'util/piano/math';
import { getTrackingQueryParams } from 'util/tracking';

import OfferInclusion from './OfferInclusion';
import { useOutsideClick } from './hooks';

enum TermPeriod {
  MONTHLY = 'monthly',
  ANNUAL = 'annual',
}

interface RadioButtonProps {
  children?: React.ReactNode;
  id: string;
  onClick?: () => void;
  selected: boolean;
}

function RadioButton({ children, id, onClick, selected }: RadioButtonProps) {
  return (
    <button
      className={clsx(
        'mb-3 flex min-h-20 w-full cursor-pointer justify-items-start rounded border-2 p-1.5 pl-3',
        selected ? 'border-black' : 'border border-neutral-200',
      )}
      id={id}
      onClick={onClick}
      type="button"
    >
      <div
        className={clsx(
          'relative size-5 place-self-center rounded-full px-2',
          selected ? 'bg-black' : 'border border-neutral-200',
        )}
      >
        <div
          className={clsx(
            'absolute left-1/2 top-1/2 size-2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-white',
            {
              hidden: !selected,
            },
          )}
        />
      </div>
      <div className="my-auto ml-3 w-full select-none text-left font-medium text-gray-900">
        {children}
      </div>
    </button>
  );
}

export default function MonthlyAnnualPaywallContent() {
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const offerId = useAppSelector((state) => state.piano.offerId);
  const terms = useAppSelector((state) => state.piano.terms);
  const user = useAppSelector((state) => state.piano.user);

  const [monthlyTerm, annualTerm] = terms;

  const isAbTestActive = useAbExperiment(AbExperiment.PaywallShowDiscount);
  const registrationOnly = useAppSelector(
    (state) =>
      state.features.piano.enabled &&
      state.features.piano.data.registrationOnly,
  );
  const hasApp = useAppSelector((state) => state.features.mobileApp.enabled);
  const hasPuzzles = useAppSelector((state) => state.conf.hasPuzzles);
  const clusterSites = useAppSelector((state) => state.cluster.sites);
  const themeVariant = useAppSelector((state) => state.conf.themeVariant);
  const siteName = useAppSelector((state) => state.conf.name);
  const [showCluster, setShowCluster] = useState(false);
  const [selectedTerm, setSelectedTerm] = useState(TermPeriod.MONTHLY);
  const termsHaveLoaded = terms.length > 0;
  const hasMultipleMonthlyPlans = monthlyTerm?.hasMultiplePlans ?? false;
  const hasMultipleAnnualPlans = annualTerm?.hasMultiplePlans ?? false;
  const isBasicPlan = monthlyTerm?.resource.type === SimpleResourceType.BASIC;
  const isAgsTheme = themeVariant === ThemeVariant.AGS;
  const showPaywall = termsHaveLoaded || registrationOnly;
  const isSupporterSite = useAppSelector(
    (state) => state.features.supporterSite.enabled,
  );

  const paywallCampaign =
    pianoFeature.enabled && pianoFeature.data.paywallCampaign;

  const buttonRef = useRef(null);
  useOutsideClick(buttonRef, () => setShowCluster(false));

  const setMonthly = useCallback(() => {
    setSelectedTerm(TermPeriod.MONTHLY);
    if (monthlyTerm) {
      sendAddToCartGa4(monthlyTerm.termId);
    }
  }, [setSelectedTerm, monthlyTerm]);

  const setAnnually = useCallback(() => {
    setSelectedTerm(TermPeriod.ANNUAL);
    if (annualTerm) {
      sendAddToCartGa4(annualTerm.termId);
    }
  }, [setSelectedTerm, annualTerm]);

  const sendAbTestClick = useCallback(() => {
    if (isAbTestActive)
      TagManager.dataLayer({
        dataLayer: {
          event: 'abtest_click',
        },
      });
  }, [isAbTestActive]);

  const onClickLogin = useCallback(() => {
    sendToGtm({
      action: 'article_page',
      label: 'continue_login_click',
      trigger: 'signup_flow',
    });
    setGtmDataLayer({
      event: Ga4EventType.PaywallInteraction,
      interaction_type: InteractionType.ClickLogin,
    });

    onPianoReady((tp) => {
      tp.pianoId
        .logout()
        .catch(console.error)
        .finally(() => redirectToLogin());
    });
  }, []);

  const onClickSubscribe = useCallback(() => {
    sendAbTestClick();
    setGtmDataLayer({
      event: Ga4EventType.PaywallInteraction,
      interaction_type: InteractionType.ClickSubscribe,
    });

    if (!monthlyTerm || !annualTerm) {
      return;
    }
    const termId =
      selectedTerm === TermPeriod.MONTHLY
        ? monthlyTerm.termId
        : annualTerm.termId;
    sendBeginCheckout(termId);

    const qs = new URLSearchParams({
      offerId,
      return: window.location.href,
      termId,
      ...getTrackingQueryParams(),
    }).toString();
    redirectToRegister(`/payment/?${qs}`);
  }, [sendAbTestClick, selectedTerm, offerId, monthlyTerm, annualTerm]);

  useEffect(() => {
    if (!termsHaveLoaded && isAbTestActive) {
      sendAbTestImpression();
    }
  }, [termsHaveLoaded, isAbTestActive]);

  useEffect(() => {
    if (!showPaywall) {
      return;
    }
    if (!annualTerm) {
      return;
    }
    sendToGtm({
      action: 'article_page',
      label: 'paywall_offer_impressions',
      trigger: 'signup_flow',
    });
    setGtmDataLayer({
      event: Ga4EventType.PaywallExpression,
      impression_type: ImpressionType.PaywallOffer,
    });
  }, [showPaywall, annualTerm]);

  useEffect(() => {
    if (!termsHaveLoaded || !monthlyTerm || !annualTerm) {
      return;
    }

    window.tracking = window.tracking || {};
    window.tracking.itemImpressions = window.tracking?.itemImpressions || [];

    const impressionData = [
      createImpressionItem(monthlyTerm, siteName, offerId),
      createImpressionItem(annualTerm, siteName, offerId),
    ];

    if (window.sessionStorage) {
      sessionStorage.setItem(
        'tracking.impressions',
        JSON.stringify(impressionData),
      );
    }

    setGa4GtmCommerce({
      event: 'view_item',
      items: [
        createEcommerceItem(monthlyTerm),
        createEcommerceItem(annualTerm),
      ],
      value: monthlyTerm.finalPlan.price + annualTerm.finalPlan.price,
    });

    sendAddToCartGa4(annualTerm.termId);
  }, [offerId, siteName, annualTerm, monthlyTerm, termsHaveLoaded]);

  useEffect(() => {
    if (paywallCampaign && paywallCampaign.termFocus) {
      setSelectedTerm(paywallCampaign.termFocus as unknown as TermPeriod);
    }
  }, [paywallCampaign]);

  if (!pianoFeature.enabled) {
    return null;
  }

  const loginOrSignup = !user && (
    <p className="text-sm text-gray-600">
      <button
        className="cursor-pointer underline decoration-gray-400 hover:decoration-gray-600"
        onClick={onClickLogin}
        type="button"
      >
        Login
      </button>{' '}
      or signup to continue reading
    </p>
  );

  const trialTextMonthly = monthlyTerm?.firstPlan.format(
    ({ duration, endless, periodFactor, periodUnit, plan, price }) => {
      const isSingleMonth =
        periodUnit === SimplePeriodUnit.MONTHS && periodFactor === 1;

      return (
        <span>
          {isSingleMonth && !monthlyTerm.type === 'dynamic'
            ? `$${price}`
            : plan.format(
                ({ period: convertedPeriod, price: convertedPrice }) =>
                  `$${convertedPrice}/${convertedPeriod}`,
                {
                  convertPeriodToSingleFactor: true,
                  truncateUnnecessaryDecimals: true,
                },
              )}
          {!endless && ` for the first ${duration}`}
        </span>
      );
    },
    {
      truncateUnnecessaryDecimals: true,
    },
  );

  const trialTextAnnual = annualTerm?.firstPlan.format(
    ({ duration, endless, periodFactor, periodUnit, plan, price }) => {
      const isSingleMonth =
        periodUnit === SimplePeriodUnit.MONTHS && periodFactor === 1;

      return (
        <span>
          {isSingleMonth
            ? `$${price}`
            : plan.format(
                ({ period: convertedPeriod, price: convertedPrice }) =>
                  `$${convertedPrice}/${convertedPeriod}`,
                {
                  convertPeriodToSingleFactor: true,
                  truncateUnnecessaryDecimals: true,
                },
              )}
          {!endless && ` for the first ${duration}`}
        </span>
      );
    },
    {
      truncateUnnecessaryDecimals: true,
    },
  );

  const monthlyRadioButtonContent = hasMultipleMonthlyPlans
    ? monthlyTerm?.finalPlan.format(
        ({ period, plan, price }) => (
          <div>
            <p className="text-base">{trialTextMonthly}</p>
            <span className="text-xs font-light text-gray-500 md:text-sm">
              {/* eslint-disable-next-line @stylistic/max-len */}
              Then, billed ${price} {plan.period.factor > 1 ? 'every' : 'per'}{' '}
              {period}
            </span>
          </div>
        ),
        {
          truncateUnnecessaryDecimals: true,
        },
      )
    : monthlyTerm?.firstPlan.format(
        ({ period, price }) => `$${price}/${period}`,
        {
          convertPeriodToSingleFactor: true,
          truncateUnnecessaryDecimals: true,
        },
      );

  const annualRadioButtonContent = hasMultipleAnnualPlans
    ? annualTerm?.finalPlan.format(
        ({ period, plan, price }) => (
          <div>
            <p className="text-base">{trialTextAnnual}</p>
            <span className="text-xs font-light text-gray-500 md:text-sm">
              {/* eslint-disable-next-line @stylistic/max-len */}
              Then, billed ${price} {plan.period.factor > 1 ? 'every' : 'per'}{' '}
              {period}
            </span>
          </div>
        ),
        {
          truncateUnnecessaryDecimals: true,
        },
      )
    : !!monthlyTerm &&
      annualTerm?.finalPlan.format(
        ({ period, plan, price }) => (
          <>
            <div className="flex w-full flex-row justify-between">
              <div>
                ${price}/{period}
              </div>
            </div>
            <span className="text-xs font-light text-gray-500 md:text-sm">
              Equivalent of $
              {formatPrice(
                plan.price *
                  calculatePeriodRatio(
                    monthlyTerm?.finalPlan.period,
                    plan.period,
                  ),
                true,
              )}{' '}
              {monthlyTerm?.finalPlan.format(
                ({ period: finalPeriod, periodFactor: finalPeriodFactor }) =>
                  `${finalPeriodFactor > 1 ? 'every' : 'per'} ${finalPeriod}`,
              )}
            </span>
          </>
        ),
        {
          truncateUnnecessaryDecimals: true,
        },
      );

  let campaignPill =
    !!monthlyTerm && !!annualTerm ? (
      <div className="z-10 -mb-2.5 mt-1 select-none rounded-full bg-green-600 px-2 font-inter text-xs font-medium uppercase leading-5 text-white">
        {formatSavingsPercentage(monthlyTerm.finalPlan, annualTerm.finalPlan)}%
        OFF
      </div>
    ) : null;

  if (paywallCampaign) {
    campaignPill = (
      <div
        className={clsx(
          'z-10 select-none rounded-full px-2 font-inter text-xs font-medium leading-5 text-white',
          paywallCampaign.pillPosition === PaywallCampaignPillPosition.TOP
            ? 'mb-2.5'
            : '-mb-2.5',
          {
            'mt-1':
              paywallCampaign.pillPosition ===
              PaywallCampaignPillPosition.ANNUAL,
          },
        )}
        style={{
          backgroundColor: paywallCampaign.pillColour,
          color: paywallCampaign.pillTextColour,
        }}
      >
        {paywallCampaign.pillText}
      </div>
    );
  }

  const showMonthlyTerm =
    !paywallCampaign ||
    (paywallCampaign &&
      paywallCampaign.termDisplay !== PaywallCampaignTermDisplay.ANNUAL);
  const showAnnualTerm =
    !paywallCampaign ||
    (paywallCampaign &&
      paywallCampaign.termDisplay !== PaywallCampaignTermDisplay.MONTHLY);

  const showPillOnMonthlyTerm =
    paywallCampaign &&
    paywallCampaign.pillPosition === PaywallCampaignPillPosition.MONTHLY;
  const showPillOnAnnualTerm =
    !paywallCampaign ||
    (paywallCampaign &&
      paywallCampaign.pillPosition === PaywallCampaignPillPosition.ANNUAL);

  return (
    <>
      <div className="mb-6 font-inter empty:hidden">{loginOrSignup}</div>
      <div className="flex w-[90%] flex-col items-center font-inter md:w-1/2">
        {showMonthlyTerm && (
          <>
            {showPillOnMonthlyTerm && campaignPill}
            <RadioButton
              id="monthly-button"
              onClick={setMonthly}
              selected={selectedTerm === TermPeriod.MONTHLY}
            >
              {monthlyRadioButtonContent}
            </RadioButton>
          </>
        )}
        {showAnnualTerm && (
          <>
            {showPillOnAnnualTerm && campaignPill}
            <RadioButton
              id="annual-button"
              onClick={setAnnually}
              selected={selectedTerm === TermPeriod.ANNUAL}
            >
              {annualRadioButtonContent}
            </RadioButton>
          </>
        )}
        <div className="mb-4 mt-3 w-full space-y-2 text-left">
          <OfferInclusion>
            {isSupporterSite ? (
              <>Unlimited access on the web</>
            ) : (
              <>All articles from our website{hasApp && ' & app'}</>
            )}
          </OfferInclusion>
          {isSupporterSite && (
            <OfferInclusion>
              Breaking news alerts direct to your inbox
            </OfferInclusion>
          )}
          {!isBasicPlan && (
            <>
              {/* eslint-disable @stylistic/max-len */}
              <OfferInclusion>
                The digital version of{' '}
                {isAgsTheme ? <>This Week&apos;s</> : <>Today&apos;s</>} Paper
              </OfferInclusion>
              {/* eslint-enable @stylistic/max-len */}
            </>
          )}
          {hasPuzzles && !isBasicPlan && (
            <OfferInclusion>Crosswords, Sudoku and Trivia</OfferInclusion>
          )}
          {isSupporterSite ? (
            <OfferInclusion> Supporting local news</OfferInclusion>
          ) : (
            <OfferInclusion>
              {isAgsTheme ? 'Our entire' : 'All other'}{' '}
              <button
                className={clsx('relative cursor-pointer whitespace-nowrap', {
                  underline: clusterSites.length > 0,
                })}
                onClick={() => {
                  if (clusterSites.length > 0) {
                    setShowCluster((curr) => !curr);
                  }
                }}
                ref={buttonRef}
                type="button"
              >
                {isAgsTheme ? 'agricultural news' : 'regional websites'}
                {showCluster && (
                  <ul className="absolute left-1/4 top-full z-50 mt-2 flex -translate-x-1/2 cursor-pointer list-inside list-disc flex-col gap-y-1 whitespace-nowrap rounded border border-gray-300 bg-white py-3 pl-4 pr-5 text-xs text-gray-600 shadow-md">
                    <div className="absolute -top-1 left-1/2 -ml-1 size-2 rotate-45 border-l border-t border-gray-300 bg-white" />
                    {clusterSites.map((site) => (
                      <li key={site.name}>{site.name}</li>
                    ))}
                  </ul>
                )}
              </button>{' '}
              {isAgsTheme ? 'network' : 'in your area'}
            </OfferInclusion>
          )}
        </div>
        <button
          className="relative mt-2 w-full rounded border-gray-700 bg-gray-950 px-3 py-2.5 text-center text-sm text-white hover:bg-gray-900"
          onClick={onClickSubscribe}
          type="button"
        >
          Continue
        </button>
        <div className="mt-6 flex w-full flex-row justify-center">
          <div className="rounded-sm border">
            <svg className="h-5 w-10" viewBox="0 -5 48 48">
              <path
                d="M37.972 13.82c.107-5.565-4.485-9.837-10.799-9.837H14.115a1.278 1.278 0 0 0-1.262 1.079L7.62 37.758a1.038 1.038 0 0 0 1.025 1.2h7.737l-1.21 7.572a1.038 1.038 0 0 0 1.026 1.2H22.5c.305 0 .576-.11.807-.307.231-.198.269-.471.316-.772l1.85-10.885c.047-.3.2-.69.432-.888.231-.198.433-.306.737-.307H30.5c6.183 0 11.43-4.394 12.389-10.507.678-4.34-1.182-8.287-4.916-10.244Z"
                fill="#001C64"
              />
              <path
                d="m18.056 26.9-1.927 12.22-1.21 7.664a1.038 1.038 0 0 0 1.026 1.2h6.67a1.278 1.278 0 0 0 1.261-1.079l1.758-11.14a1.277 1.277 0 0 1 1.261-1.078h3.927c6.183 0 11.429-4.51 12.388-10.623.68-4.339-1.504-8.286-5.238-10.244-.01.462-.05.923-.121 1.38-.959 6.112-6.206 10.623-12.389 10.623h-6.145a1.277 1.277 0 0 0-1.261 1.077Z"
                fill="#0070E0"
              />
              <path
                d="M16.128 39.12h-7.76a1.037 1.037 0 0 1-1.025-1.2l5.232-33.182a1.277 1.277 0 0 1 1.262-1.078h13.337c6.313 0 10.905 4.595 10.798 10.16-1.571-.824-3.417-1.295-5.44-1.295H21.413a1.278 1.278 0 0 0-1.261 1.078L18.057 26.9l-1.93 12.22Z"
                fill="#003087"
              />
            </svg>
          </div>

          <div className="mx-2 rounded-sm border">
            <svg className="h-6 w-10" viewBox="0 0 33 17">
              <path
                clipRule="evenodd"
                d="M.2 2.196C.2.983 1.183 0 2.396 0h28.008C31.617 0 32.6.983 32.6 2.196v15.858a2.196 2.196 0 01-2.196 2.196H2.396A2.196 2.196 0 01.2 18.054V2.196z"
                fill="#fff"
                fillRule="evenodd"
              />
              <path
                d="M19.78 3.396h-5.749v10.077h5.75V3.396z"
                fill="#FF5F00"
              />
              <path
                d="M14.396 8.435a6.269 6.269 0 01.66-2.802 6.43 6.43 0 011.85-2.237 6.663 6.663 0 00-3.36-1.334 6.716 6.716 0 00-3.572.608 6.517 6.517 0 00-2.698 2.364 6.292 6.292 0 00-1.001 3.4c0 1.203.347 2.381 1 3.4a6.517 6.517 0 002.699 2.364 6.716 6.716 0 003.572.608 6.662 6.662 0 003.36-1.333 6.43 6.43 0 01-1.85-2.237 6.278 6.278 0 000-5.602 6.442 6.442 0 00-1.85-2.238 6.663 6.663 0 013.36-1.333 6.716 6.716 0 013.572.607 6.517 6.517 0 012.699 2.364 6.292 6.292 0 011 3.4v.002zM26.91 12.405V12.2h.085v-.043h-.217v.043h.093v.206h.038zm.42 0v-.25h-.065l-.076.179-.077-.178h-.059v.25h.048v-.188l.071.162h.05l.07-.162v.189l.039-.002z"
                fill="#EB001B"
              />
              <path
                d="M27.538 8.435c0 1.203-.347 2.381-1.001 3.4a6.517 6.517 0 01-2.699 2.364 6.717 6.717 0 01-3.573.608 6.663 6.663 0 01-3.359-1.334 6.441 6.441 0 001.85-2.238 6.278 6.278 0 000-5.602 6.442 6.442 0 00-1.85-2.238 6.663 6.663 0 013.36-1.333 6.716 6.716 0 013.572.607 6.517 6.517 0 012.699 2.364 6.292 6.292 0 011 3.4v.002zM26.91 12.405V12.2h.085v-.043h-.217v.043h.093v.206h.038zm.42 0v-.25h-.065l-.076.179-.077-.178h-.059v.25h.048v-.188l.071.162h.05l.07-.162v.189l.039-.002z"
                fill="#F79E1B"
              />
            </svg>
          </div>

          <div className="rounded-sm border">
            <svg className="h-6 w-10" viewBox="0 2 33 17">
              <path
                clipRule="evenodd"
                d="M0 2.196C0 .983.983 0 2.196 0h28.008C31.417 0 32.4.983 32.4 2.196v15.858a2.196 2.196 0 01-2.196 2.196H2.196A2.196 2.196 0 010 18.054V2.196z"
                fill="#FFFFFF"
                fillRule="evenodd"
              />
              <path
                clipRule="evenodd"
                d="M16.3 8.268c-.015 1.114 1.075 1.735 1.895 2.105.844.379 1.127.622 1.124.961-.006.52-.673.748-1.296.757-1.089.016-1.721-.271-2.225-.488l-.392 1.694c.505.215 1.44.402 2.409.41 2.274 0 3.763-1.037 3.77-2.645.01-2.04-3.055-2.154-3.035-3.066.008-.277.293-.572.92-.647.31-.038 1.165-.067 2.135.346l.38-1.64a6.229 6.229 0 00-2.026-.343c-2.141 0-3.647 1.05-3.66 2.556zm9.344-2.415c-.415 0-.765.224-.921.567l-3.25 7.167h2.273l.453-1.155h2.777l.263 1.155h2.003l-1.748-7.734h-1.85zm.318 2.09l.656 2.903h-1.796l1.14-2.904zm-12.418-2.09l-1.792 7.734h2.166l1.791-7.734h-2.165zm-3.205 0l-2.254 5.264-.912-4.476c-.107-.5-.53-.788-.999-.788H2.488l-.051.224c.756.152 1.616.397 2.137.658.319.16.41.3.514.68l1.728 6.172h2.289l3.51-7.734h-2.276z"
                fill="#1A1F71"
                fillRule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
    </>
  );
}
