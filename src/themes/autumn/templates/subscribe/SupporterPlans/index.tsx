import clsx from 'clsx';
import { useCallback, useEffect, useState } from 'react';

import PianoHook from 'components/Piano/PianoHook';
import { useAppSelector } from 'store/hooks';
import SubscriptionFooter from 'themes/autumn/components/footer/SubscriptionFooter';
import Container from 'themes/autumn/components/generic/Container';
import Skeleton from 'themes/autumn/components/generic/Skeleton';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import SubscriptionNav from 'themes/autumn/components/nav/SubscriptionNav';
import { SimplePeriodUnit } from 'types/Piano';
import { redirectToRegister } from 'util/auth';
import {
  createEcommerceItem,
  createImpressionItem,
  setGa4GtmCommerce,
} from 'util/gtm';
import { getTrackingQueryParams } from 'util/tracking';

import FaqContainerACM from '../common/FaqContainerACM';

import Benefits from './Benefits';
import Option from './Option';
import Terms from './Terms';
import PaymentMethods from './icons/payment-methods.svg';

import type React from 'react';

const PLANS = [
  {
    footnote: 'For the news you rely on',
    heading: 'Supporter',
  },
  {
    footnote: 'For the future you believe in',
    heading: 'Partner',
  },
  {
    footnote: 'For the community you love',
    heading: 'Champion',
  },
];

function SubscribePageBendigoHealth(): React.ReactElement | null {
  const domain = useAppSelector((state) => state.conf.domain);
  const siteName = useAppSelector((state) => state.conf.name);
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const terms = useAppSelector((state) => state.piano.terms);
  const offerId = useAppSelector((state) => state.piano.offerId);
  const staticSiteUrl = useAppSelector(
    (state) => state.settings.staticSiteUrl,
  );
  const [selectedTerm, setSelectedTerm] = useState<number>(0);

  const [t1Term, t2Term, t3Term, monthlyTerm] = terms;
  const termsHaveLoaded = terms.length > 0;

  const isLoading = terms.length !== 4;

  const onClickSubscribe = useCallback(() => {
    const term = terms[selectedTerm];
    if (!term || !offerId) {
      return;
    }

    setGa4GtmCommerce({
      event: 'begin_checkout',
      items: [
        {
          category: 'Subscription',
          id: term.termId,
          name: term.name,
          price: term.firstPlan.price,
        },
      ],
      value: term.firstPlan.price,
    });

    const searchQueryArgs = new URLSearchParams({
      ...getTrackingQueryParams(),
      offerId,
      selectedTermIdx: selectedTerm.toString(),
      termId: term.termId,
    });
    redirectToRegister(`/payment/?${searchQueryArgs.toString()}`);
  }, [selectedTerm, terms, offerId]);

  useEffect(() => {
    if (!termsHaveLoaded || !t1Term || !t2Term || !t3Term || !monthlyTerm) {
      return;
    }

    window.tracking = window.tracking || {};
    window.tracking.itemImpressions = window.tracking?.itemImpressions || [];

    const impressionData = [
      createImpressionItem(t1Term, siteName, offerId),
      createImpressionItem(t2Term, siteName, offerId),
      createImpressionItem(t3Term, siteName, offerId),
      createImpressionItem(monthlyTerm, siteName, offerId),
    ];

    if (window.sessionStorage) {
      sessionStorage.setItem(
        'tracking.impressions',
        JSON.stringify(impressionData),
      );
    }

    setGa4GtmCommerce({
      event: 'view_item',
      items: [
        createEcommerceItem(t1Term),
        createEcommerceItem(t2Term),
        createEcommerceItem(t3Term),
        createEcommerceItem(monthlyTerm),
      ],
      value:
        t1Term.finalPlan.price +
        t2Term.finalPlan.price +
        t3Term.finalPlan.price +
        monthlyTerm.finalPlan.price,
    });
  }, [
    offerId,
    siteName,
    t1Term,
    t2Term,
    t3Term,
    monthlyTerm,
    termsHaveLoaded,
  ]);

  const trialTextMonthly = monthlyTerm?.firstPlan.format(
    ({ duration, endless, periodFactor, periodUnit, plan, price }) => {
      const isSingleMonth =
        periodUnit === SimplePeriodUnit.MONTHS && periodFactor === 1;

      const priceText =
        isSingleMonth && !monthlyTerm.resource.type
          ? `$${price}`
          : plan.format(
              ({ period: convertedPeriod, price: convertedPrice }) =>
                `$${convertedPrice}/${convertedPeriod}`,
              {
                convertPeriodToSingleFactor: true,
                truncateUnnecessaryDecimals: true,
              },
            );

      const durationText = !endless ? ` For the first ${duration}` : '';

      return { durationText, priceText };
    },
    {
      truncateUnnecessaryDecimals: true,
    },
  );

  const monthlySubtitle = monthlyTerm?.finalPlan.format(
    ({ period, price }) => `Then $${price}/${period}`,
    {
      truncateUnnecessaryDecimals: true,
    },
  );

  if (!pianoFeature.enabled) {
    return null;
  }

  return (
    <TemplateWrapper
      className="font-inter text-gray-900"
      footer={<SubscriptionFooter showLinksOnDesktop />}
      free
      hideSmartBanner
      nav={<SubscriptionNav showContact={false} showHelp={false} showLogin />}
    >
      <div className="relative z-0 h-[241px] w-full bg-stone-100 text-white md:aspect-[3000/762] md:h-auto">
        <picture className={clsx('h-full object-cover md:h-auto md:w-full')}>
          <source
            media="(min-width:768px)"
            srcSet={`${staticSiteUrl}images/supporter/desktop.jpg`}
          />
          <img
            alt="Support the news"
            className="h-full object-cover md:w-full"
            src={`${staticSiteUrl}images/supporter/mobile.jpg`}
          />
        </picture>
      </div>
      <div className="mx-auto w-full">
        <PianoHook
          className="hidden h-0"
          id="subscription-select-wrap-fullpage"
        />
        <div className="w-full bg-gray-100">
          <div className="mx-auto flex w-full max-w-344 flex-col gap-3 py-8 md:max-w-full md:pb-28 md:pt-16">
            <div className="flex flex-col gap-3 md:gap-4">
              <div className="w-full text-center text-sm md:text-base">
                Enjoy convenient access to the news that matters most in{' '}
                {domain.includes('gazette')
                  ? 'the Mountains'
                  : 'St George & the Shire'}
                .
                <br className="hidden md:block" />
                Your support helps keep our community strong, informed and
                connected.
              </div>
              <div className="mt-3 w-full text-center text-sm font-semibold leading-6">
                Select your level of support
              </div>
            </div>
            <div className="flex flex-col justify-center gap-3 md:flex-row md:gap-4">
              {PLANS.map(({ footnote, heading }, idx) => (
                <Option
                  footnote={footnote}
                  heading={heading}
                  key={heading}
                  onClick={() => setSelectedTerm(idx)}
                  selected={selectedTerm === idx}
                  term={terms[idx]}
                />
              ))}
            </div>
            <div className="mt-2 w-full text-center text-sm font-semibold leading-6">
              Monthly option
            </div>
            <div className="flex flex-col justify-center gap-3 md:flex-row md:gap-4">
              <Option
                footnote={monthlySubtitle ?? ''}
                heading={trialTextMonthly?.priceText ?? 'Monthly'}
                onClick={() => setSelectedTerm(3)}
                selected={selectedTerm === 3}
                showMainPriceText={false}
                subheading={trialTextMonthly?.durationText ?? ''}
                term={monthlyTerm}
              />
            </div>

            <Skeleton
              className="-mt-1 md:mx-auto md:mt-0"
              showContent={!isLoading}
            >
              <button
                className="mt-2 flex h-12 w-full items-center justify-center rounded-lg bg-gray-950 text-base font-semibold leading-none text-white hover:bg-gray-900 md:w-[312px] md:text-lg"
                onClick={onClickSubscribe}
                type="button"
              >
                Subscribe
              </button>
            </Skeleton>
            <div className="text-center text-sm font-semibold leading-6 underline">
              No lock in contract
            </div>
            <div className="mx-auto">
              <PaymentMethods />
            </div>
            <div className="mx-auto hidden w-full max-w-screen-md text-center text-xs leading-normal text-gray-500 md:block">
              <Terms />
            </div>
          </div>
        </div>
        <div className="mx-auto w-full max-w-344 py-10 md:max-w-6xl md:px-7 md:py-16">
          <Benefits />
        </div>
        <div className="mx-auto w-full max-w-344 text-center text-xs leading-normal text-gray-500 md:hidden">
          <Terms />
        </div>
      </div>
      <div className="mx-auto mt-12 max-w-[940px] lg:mt-16">
        <Container>
          <FaqContainerACM />
        </Container>
      </div>
    </TemplateWrapper>
  );
}

export default SubscribePageBendigoHealth;
