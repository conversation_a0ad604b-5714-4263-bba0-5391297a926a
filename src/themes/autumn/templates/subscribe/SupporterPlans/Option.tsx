import clsx from 'clsx';

import Skeleton from 'themes/autumn/components/generic/Skeleton';

import type { SimpleTerm } from 'types/Piano';

interface OptionProps {
  footnote: string;
  heading: string;
  onClick: () => void;
  selected: boolean;
  showMainPriceText?: boolean;
  subheading?: string;
  term?: SimpleTerm;
}

export default function Option({
  footnote,
  heading,
  onClick,
  selected,
  showMainPriceText = true,
  subheading = '',
  term,
}: OptionProps) {
  return (
    <button
      className={clsx(
        'rounded-2xl bg-white px-3.5 pb-3 pt-3.5 transition-colors duration-200 md:w-[264px] md:pb-4.5 md:pt-5',
        {
          'border-2 border-gray-300': !selected,
          'border-2 border-gray-900': selected,
        },
      )}
      onClick={onClick}
      type="button"
    >
      <span className="flex items-center gap-x-2.5 md:items-start">
        <span
          className={clsx('block size-5 rounded-full md:mt-0.5', {
            "after:content-[' '] relative bg-gray-900 after:absolute after:left-1/2 after:top-1/2 after:h-2 after:w-2 after:-translate-x-1/2 after:-translate-y-1/2 after:rounded-full after:bg-white":
              selected,
            'border border-gray-900': !selected,
          })}
        />
        <span className="flex flex-col items-start gap-y-1">
          <span className="inline-flex items-center gap-x-2 font-merriweather text-lg font-bold">
            {heading}
            {showMainPriceText && (
              <Skeleton showContent={!!term} useSpan>
                <>
                  {term?.finalPlan.format(
                    (info) => `$${info.price}/${info.period}`,
                    {
                      truncateUnnecessaryDecimals: true,
                    },
                  ) ?? 'Loading price'}
                </>
              </Skeleton>
            )}
          </span>
          <span className="text-left font-medium">{subheading}</span>
          <span className="text-left text-sm text-gray-600">{footnote}</span>
        </span>
      </span>
    </button>
  );
}
