// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Carousel Component Component rendering and props renders with required props and displays stories 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <div
    class="-mx-4 my-5.5 md:-mx-6 xl:-mr-6 xl:ml-0"
  >
    <h2
      class="mx-4 mb-6 overflow-y-visible text-lg font-semibold md:mx-6 md:mb-0 xl:mx-0 font-inter"
    >
      Test Carousel
    </h2>
    <div
      class="slider-wrapper my-6 w-full md:my-7 md:px-6 xl:pl-0 slickWrapper"
    >
      <div
        class="flex flex-row left-align-mode"
        data-testid="mock-slider"
      >
        <button
          data-testid="mock-prev-arrow"
          type="button"
        >
          Previous
        </button>
        <button
          data-testid="mock-next-arrow"
          type="button"
        >
          Next
        </button>
        <div
          data-testid="slider-content"
        >
          <div
            data-testid="carousel-item-100000"
          >
            <h3>
              test title 0
            </h3>
            <button
              data-testid="story-click-100000"
              type="button"
            >
              Click Story
            </button>
          </div>
          <div
            data-testid="carousel-item-100001"
          >
            <h3>
              test title 1
            </h3>
            <button
              data-testid="story-click-100001"
              type="button"
            >
              Click Story
            </button>
          </div>
          <div
            data-testid="carousel-item-100002"
          >
            <h3>
              test title 2
            </h3>
            <button
              data-testid="story-click-100002"
              type="button"
            >
              Click Story
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
