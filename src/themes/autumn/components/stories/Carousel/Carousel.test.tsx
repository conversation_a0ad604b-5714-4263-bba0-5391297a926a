import { render, screen } from '@testing-library/react';
import React from 'react';

import { createStore } from 'store/store';
import { TestWrapper, genMockStories } from 'util/jest';

import { CarouselMobileMode, ContainerType } from './enums';

import { Carousel } from '.';

import type { Story } from 'types/Story';

interface MockSliderProps {
  beforeChange?: (oldIndex: number, newIndex: number) => void;
  children: React.ReactNode;
  className?: string;
}

interface MockCarouselItemProps {
  onClick?: () => void;
  story: Story;
}

jest.mock('react-slick', () => {
  const MockSlider = ({
    beforeChange,
    children,
    className,
  }: MockSliderProps) => (
    <div className={className} data-testid="mock-slider">
      <button
        data-testid="mock-prev-arrow"
        onClick={() => beforeChange?.(1, 0)}
        type="button"
      >
        Previous
      </button>
      <button
        data-testid="mock-next-arrow"
        onClick={() => beforeChange?.(0, 1)}
        type="button"
      >
        Next
      </button>
      <div data-testid="slider-content">{children}</div>
    </div>
  );
  return MockSlider;
});

jest.mock(
  './CarouselItem',
  () =>
    function MockCarouselItem({ onClick, story }: MockCarouselItemProps) {
      return (
        <div data-testid={`carousel-item-${story.id}`}>
          <h3>{story.title}</h3>
          <button
            data-testid={`story-click-${story.id}`}
            onClick={onClick}
            type="button"
          >
            Click Story
          </button>
        </div>
      );
    },
);

describe('Carousel Component', () => {
  const mockStories = genMockStories({ length: 3 });
  const store = createStore();

  const defaultProps = {
    stories: mockStories,
    title: 'Test Carousel',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component rendering and props', () => {
    it('renders with required props and displays stories', () => {
      const { container } = render(
        <TestWrapper store={store}>
          <Carousel
            stories={defaultProps.stories}
            title={defaultProps.title}
          />
        </TestWrapper>,
      );

      // check that the carousel renders
      expect(screen.getByTestId('mock-slider')).toBeInTheDocument();

      // check that all stories are rendered
      mockStories.forEach((story) => {
        const carouselItem = screen.getByTestId(`carousel-item-${story.id}`);
        expect(carouselItem).toBeInTheDocument();
        expect(screen.getByText(story.title)).toBeInTheDocument();
      });

      expect(screen.getByText('Test Carousel')).toBeInTheDocument();

      expect(container.firstChild).toMatchSnapshot();
    });

    it('renders with optional props correctly', () => {
      render(
        <TestWrapper store={store}>
          <Carousel
            container={ContainerType.MAIN}
            mobileMode={CarouselMobileMode.CENTER_MODE}
            stories={defaultProps.stories}
            title={defaultProps.title}
            url="/test-url"
            useStrapHeading
          />
        </TestWrapper>,
      );

      // check that the slider has the correct mobile mode class
      const slider = screen.getByTestId('mock-slider');
      expect(slider).toHaveClass('center-mode');
      expect(slider).not.toHaveClass('left-align-mode');
    });

    it('applies correct mobile mode classes', () => {
      const { rerender } = render(
        <TestWrapper store={store}>
          <Carousel
            mobileMode={CarouselMobileMode.LEFT_ALIGN_MODE}
            stories={defaultProps.stories}
            title={defaultProps.title}
          />
        </TestWrapper>,
      );

      let slider = screen.getByTestId('mock-slider');
      expect(slider).toHaveClass('left-align-mode');
      expect(slider).not.toHaveClass('center-mode');

      rerender(
        <TestWrapper store={store}>
          <Carousel
            mobileMode={CarouselMobileMode.CENTER_MODE}
            stories={defaultProps.stories}
            title={defaultProps.title}
          />
        </TestWrapper>,
      );

      slider = screen.getByTestId('mock-slider');
      expect(slider).toHaveClass('center-mode');
      expect(slider).not.toHaveClass('left-align-mode');
    });
  });

  it('handles empty stories array gracefully', () => {
    render(
      <TestWrapper store={store}>
        <Carousel stories={[]} title={defaultProps.title} />
      </TestWrapper>,
    );

    // should still render the carousel structure
    expect(screen.getByTestId('mock-slider')).toBeInTheDocument();
    expect(screen.getByText('Test Carousel')).toBeInTheDocument();

    expect(screen.queryByTestId(/carousel-item-/)).not.toBeInTheDocument();
  });

  it('passes correct settings to slider based on container type', () => {
    render(
      <TestWrapper store={store}>
        <Carousel
          container={ContainerType.MAIN}
          stories={defaultProps.stories}
          title={defaultProps.title}
        />
      </TestWrapper>,
    );

    const slider = screen.getByTestId('mock-slider');
    expect(slider).toBeInTheDocument();
  });
});
