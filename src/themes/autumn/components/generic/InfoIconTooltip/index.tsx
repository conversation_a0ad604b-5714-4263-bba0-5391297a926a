import clsx from 'clsx';
import { useState } from 'react';
import { twMerge } from 'tailwind-merge';

import { DeviceType } from 'util/device';
import { useDeviceTypeFromWidth } from 'util/hooks';

interface Props {
  children: React.ReactNode;
  className?: string;
  containerClassName?: string;
}

export default function InfoIconTooltip({
  children,
  className,
  containerClassName,
}: Props) {
  const [showTooltip, setShowTooltip] = useState(false);
  const deviceType = useDeviceTypeFromWidth();
  const isMobile = deviceType === DeviceType.MOBILE;

  return (
    <div className="relative inline-block">
      <button
        aria-controls="tooltip-content"
        aria-expanded={showTooltip}
        aria-haspopup="dialog"
        aria-label="Information Tooltip Button"
        className="cursor-pointer"
        onClick={() => setShowTooltip(!showTooltip)}
        onMouseEnter={() => !isMobile && setShowTooltip(true)}
        onMouseLeave={() => !isMobile && setShowTooltip(false)}
        type="button"
      >
        <svg fill="none" height="18" viewBox="0 0 18 18" width="18">
          <path
            d="M9.83333 12.3333H9V9H8.16667M9 5.66667H9.00833M16.5 9C16.5 13.1421 13.1421 16.5 9 16.5C4.85786 16.5 1.5 13.1421 1.5 9C1.5 4.85786 4.85786 1.5 9 1.5C13.1421 1.5 16.5 4.85786 16.5 9Z"
            stroke="#9CA3AF"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
          />
        </svg>
      </button>
      {showTooltip && (
        <div
          className={twMerge(
            'absolute left-1/2 top-full z-40 flex -translate-x-1/2',
            containerClassName,
          )}
        >
          <div
            className={clsx(
              'mt-2 min-h-fit cursor-pointer list-inside list-disc flex-col gap-y-1 rounded border border-gray-300 bg-white py-3 pl-4 pr-5 text-xs text-gray-600 shadow-md',
              className,
            )}
            id="tooltip-content"
          >
            {children}
          </div>
          <div className="absolute left-1/2 top-1 z-50 -ml-1 size-2 rotate-45 border-l border-t border-gray-300 bg-white" />
        </div>
      )}
    </div>
  );
}
