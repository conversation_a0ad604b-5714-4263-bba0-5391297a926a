/* eslint-disable @typescript-eslint/naming-convention */

import TagManager from 'react-gtm-module';

import { MailProvider } from 'store/slices/features';
import pianoSlice from 'store/slices/piano';
import { AbExperiment } from 'types/ab-tests';
import {
  changePianoAccount,
  getReturnQueryValue,
  redirectToRegister,
  returnToURL,
} from 'util/auth';
import { setCookie } from 'util/cookies';
import {
  findImpressionItem,
  sendAbTestImpression,
  sendBeginCheckout,
  sendToGtm,
  setGa4GtmCommerce,
  setGtmDataLayer,
} from 'util/gtm';
import { registerNewsletter } from 'util/mail';
import { setPianoTrackingId } from 'util/piano';
import { simplifyPianoTerm } from 'util/piano/simplify';
import { getTrackingQueryParams } from 'util/tracking';

import { onPianoReady } from './ready';

import type {
  PianoExtendedUserCallbackData,
  PianoModalDetails,
  PianoTerm,
  PianoTrackingImpression,
  PianoTrackingProduct,
} from 'types/Piano';
import type { SendToGtmProps, SetGtmDataLayerProps } from 'util/gtm';

export function onLogoutSuccess(): void {
  if (window.location.pathname !== '/my-account/') {
    window.location.reload();
  }
}

export const pianoApiOrigins = [
  'https://id-au.piano.io',
  'https://buy-au.piano.io',
  'https://sandbox.tinypass.com',
  'https://sandbox.piano.io',
  'http://localhost:3000',
];

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type PipelineHandler<P = any, S = unknown> = (
  payload: P,
  send: (payload: S) => void,
) => void;

/**
 * Get suzuka endpoint for current environment
 */

interface GetSuzukaResponse {
  url: string;
}

const get_suzuka: PipelineHandler<unknown, GetSuzukaResponse> = (
  _payload,
  send,
) => {
  const store = window.getStore();
  send({
    url: store.getState().racetracks.suzukaUrl,
  });
};

/**
 * Open mailing list signup form
 */

const open_mail_form: PipelineHandler = () => {
  const store = window.getStore();
  const { mail } = store.getState().features;
  if (mail.enabled) {
    if (mail.data.provider !== MailProvider.MARKETING_CLOUD) {
      window.open(
        `${mail.data.mailchimpListManageUrl}subscribe/post?` +
          `u=${mail.data.mailchimpAccountId}&` +
          `id=${mail.data.listId}`,
        '_blank',
        'noopener',
      );
    } else if (mail.data.marketingCloudUrl) {
      window.open(mail.data.marketingCloudUrl, '_blank', 'noopener');
    }
  }
};

/**
 * Trigger a gtm event
 */

type GtmEventRequest = SendToGtmProps;

const gtm_event: PipelineHandler<GtmEventRequest> = ({
  action,
  label,
  trigger,
  value,
}) => {
  sendToGtm({ action, label, trigger, value });
};

/**
 * Set a gtm data layer
 */

const gtm_datalayer: PipelineHandler<SetGtmDataLayerProps> = (data) => {
  setGtmDataLayer(data);
};

/**
 * Set last shown template
 */

const gtm_set_template: PipelineHandler = () => {
  // TODO: Find where this is used and implement in state?
  // lastShownTemplateLabel = payload.params;
};

/**
 * Define impressions
 */

const gtm_set_impressions: PipelineHandler<PianoTrackingImpression[]> = (
  data,
) => {
  window.tracking = window.tracking || {};
  window.tracking.impressions = data;
  window.tracking.itemImpressions = window.tracking?.itemImpressions || [];
  if (window.sessionStorage) {
    sessionStorage.setItem('tracking.impressions', JSON.stringify(data));
  }

  const items = data.filter(
    (item) => !window.tracking?.itemImpressions?.includes(item.id),
  );
  setGa4GtmCommerce({
    event: 'view_item',
    items,
    value: Number(
      items.reduce((total, item) => total + Number(item.price), 0).toFixed(2),
    ),
  });

  items.forEach((item) => window.tracking?.itemImpressions?.push(item.id));
};

/**
 * Add to cart
 */

interface GtmCartSelection {
  coupon: string;
  paymentInfo: string;
  term: string;
  value: number;
}

const gtm_add_to_cart: PipelineHandler<GtmCartSelection> = (data) => {
  window.tracking = window.tracking || {};
  const impression = findImpressionItem(data.term);
  if (!impression) {
    return;
  }

  setGa4GtmCommerce({
    ...data,
    event: 'add_payment_info',
    items: [impression],
  });

  window.tracking.cart = {
    paymentInfo: data.paymentInfo,
    products: [
      {
        brand: impression.brand,
        category: impression.category,
        id: impression.id,
        list: impression.list,
        name: impression.name,
        price: Number(impression.price),
        quantity: 1,
        variant: impression.variant,
      },
    ],
  };

  setGtmDataLayer({ ecommerce: null });
  setGtmDataLayer({
    ecommerce: {
      add: {
        actionField: {
          list: window.tracking.cart?.products[0]?.list,
        },
        products: window.tracking.cart.products.map((product) => {
          const clone: Partial<PianoTrackingProduct> = { ...product };
          delete clone.list;
          return clone;
        }),
      },
      currencyCode: 'AUD',
    },
  });
  sendToGtm({
    action: 'addToCart',
    label: 'payment_method_selected',
    trigger: 'addToCart',
    value: data.paymentInfo,
  });
};

/**
 * Remove from cart
 */
export function gtmRemoveFromCart() {
  if (!window.tracking?.cart) {
    return;
  }

  setGtmDataLayer({ ecommerce: null });
  setGtmDataLayer({
    ecommerce: {
      currencyCode: 'AUD',
      remove: {
        actionField: {
          list: window.tracking.cart?.products[0]?.list,
        },
        products: window.tracking.cart.products.map((product) => {
          const clone: Partial<PianoTrackingProduct> = { ...product };
          delete clone.list;
          return clone;
        }),
      },
    },
  });
  sendToGtm({
    action: 'removeFromCart',
    label: 'payment_method_unselected',
    trigger: 'removeFromCart',
    value: window.tracking.cart.paymentInfo,
  });
  window.tracking.cart = undefined;
}

interface GtmPromoApplied {
  code?: string;
}

const gtm_promo_applied: PipelineHandler<GtmPromoApplied> = (data) => {
  sendToGtm({
    action: 'click',
    label: 'promoApplied',
    value: 1,
  });

  if (data?.code) {
    setGtmDataLayer({
      code: data.code,
      event: 'add_code',
    });
  }
};

interface GtmGiftImpression {
  termId: string;
}

const gtm_gift_impression: PipelineHandler<GtmGiftImpression> = (data) => {
  const item = findImpressionItem(data.termId);
  if (item) {
    setGtmDataLayer({
      event: 'gift_view',
      gift: item.name,
      page_location: window.location.href,
      page_title: document.title,
      type: 'virtual',
    });
  }
};

interface GtmCommercialCheckOut {
  option: string;
  step: string;
}

const gtm_remove_from_cart: PipelineHandler = () => {
  gtmRemoveFromCart();
};

const gtm_set_commercial_checkout: PipelineHandler<GtmCommercialCheckOut> = (
  data,
) => {
  if (!window.tracking?.cart) {
    return;
  }

  setGtmDataLayer({ ecommerce: null });
  setGtmDataLayer({
    ecommerce: {
      checkout: {
        actionField: {
          option: data.option,
          step: data.step,
        },
        products: window.tracking.cart.products.map((product) => {
          const clone: Partial<PianoTrackingProduct> = { ...product };
          delete clone.list;
          return clone;
        }),
      },
    },
  });
  sendToGtm({
    action: 'checkout',
    label: data.option,
    trigger: 'checkout',
    value: Number(window.tracking.cart.products?.[0]?.price ?? 0),
  });
};

interface GtmCommercialPurchase {
  affiliation: string;
  coupon: string;
  id: string;
  isAbTest: boolean;
  revenue: string;
  shipping: string;
  tax: number;
  transactionId: string;
  type: string;
  value: string;
}

const gtm_set_commercial_purchase: PipelineHandler<GtmCommercialPurchase> = (
  data,
) => {
  const store = window.getStore();
  const abtest_id = store.getState().settings.abTest?.id;
  const item = findImpressionItem(data.id);
  if (item) {
    setGa4GtmCommerce({
      coupon: data.coupon,
      event: 'purchase',
      items: [item],
      tax: Number(data.tax),
      transactionId: data.transactionId,
      value: Number(data.value),
    });
    if (abtest_id === AbExperiment.PaymentPageCta2) {
      TagManager.dataLayer({
        dataLayer: {
          event: 'abtest_click',
        },
      });
    }
  }
};

/**
 * Set Cookie
 */

interface SetCookieRequest {
  cookieName: string;
  cookiePath: string;
  cookieValue: string;
  expireDay: number;
}

const set_cookie: PipelineHandler = ({
  cookieName,
  cookiePath,
  cookieValue,
  expireDay,
}: SetCookieRequest) => {
  setCookie({
    cookieName,
    cookiePath,
    cookieValue,
    expireSeconds: expireDay,
  });
};

/**
 * Newsletter registration
 */

interface RegisterNewsletterRequest {
  email: string;
}

const register_newsletter: PipelineHandler<RegisterNewsletterRequest> = (
  payload,
) => {
  registerNewsletter(payload.email);
};

/**
 * Define the current modal for use when modal is closed
 */

interface SetModalDetailsRequest {
  modalName: string;
  modalParams: PianoModalDetails;
}

const set_modal_details: PipelineHandler<SetModalDetailsRequest> = (
  payload,
) => {
  onPianoReady((tp) => {
    // eslint-disable-next-line no-param-reassign
    tp.modalDetails = payload;
  });
};

/**
 * Force reload on logout within piano
 */

const logout: PipelineHandler = () => {
  onLogoutSuccess();
};

/**
 * Send hash portion of the url
 */

interface GetHashResponse {
  hash: string;
}

const get_hash: PipelineHandler<unknown, GetHashResponse> = (
  _payload,
  send,
) => {
  send({
    hash: window.location.hash,
  });
};

/**
 * Set that the user has seen the login screen
 */

const has_visited_login: PipelineHandler = () => {
  onPianoReady((tp) => {
    // eslint-disable-next-line no-param-reassign
    tp.hasVisitedLogin = true;
  });
};

/**
 * Add has-rendered-paywall class to body for story paywall to prevent overflow
 */

const has_rendered_paywall: PipelineHandler = () => {
  document.body.classList.add('has-rendered-paywall');
};

/**
 * redirect users to complete-profile page after payment
 * isPrint param determines whether billing info fields are required or not
 */
interface RedirectCompleteProfileRequest {
  isPrint: boolean;
  termDescription?: string;
}

const redirect_complete_profile: PipelineHandler<
  RedirectCompleteProfileRequest
> = (data) => {
  onPianoReady((tp) => {
    const user = tp.pianoId.getUser();
    const passwordType = user?.passwordType ?? '';
    const { isPrint = false, termDescription = '' } = data;
    const hasSurvey = termDescription.includes('survey');
    const params = new URLSearchParams(window.location.search);
    const selectedTermIdx = params.get('selectedTermIdx');

    if (
      !['passwordless', 'passwordExpired'].includes(passwordType) &&
      !isPrint &&
      !hasSurvey
    ) {
      returnToURL({
        additionalParams: {
          msg: 'access',
          ...(selectedTermIdx && { selectedTermIdx }),
        },
      });
      return;
    }

    const returnParam = getReturnQueryValue();
    const source = params.get('source');

    const qs = new URLSearchParams({
      isPrint: isPrint.toString(),
      purchase: 'true',
      termDescription,
      ...(selectedTermIdx && { selectedTermIdx }),
      ...(source && { source }),
      ...(returnParam && { return: returnParam }),
      ...getTrackingQueryParams(),
    }).toString();

    const pageSlug = hasSurvey ? 'promo-survey' : 'complete-profile';
    window.location.href = `/${pageSlug}/?${qs}`;
  });
};

const redirect_to_manage_seats: PipelineHandler = () => {
  window.location.href = '/manage-seats/';
};

const redirect_to_newsletters: PipelineHandler = () => {
  window.location.href = '/newsletters/';
};

const redirect_to_homepage: PipelineHandler = () => {
  window.location.href = '/';
};
/**
 * redirect users from subscribe page to register page with offerId and termId
 */

interface RedirectToRegisterRequest {
  isGift?: boolean;
  offerId: string;
  returnUrl: string;
  termId: string;
  trackingId?: string;
}

const redirect_to_register: PipelineHandler<RedirectToRegisterRequest> = ({
  isGift,
  offerId,
  returnUrl = '',
  termId,
  trackingId,
}) => {
  sendBeginCheckout(termId);
  setPianoTrackingId(trackingId);

  const qs = new URLSearchParams({
    offerId,
    termId,
    ...(returnUrl && { return: returnUrl }),
    ...(isGift && { isGift: 'true' }),
    ...getTrackingQueryParams(),
  }).toString();

  redirectToRegister(`/payment/?${qs}`);
};

/**
 * Pass begin_checkout event to GTM
 */

interface BeginCheckoutRequest {
  termId: string;
}

const begin_checkout: PipelineHandler<BeginCheckoutRequest> = ({ termId }) => {
  sendBeginCheckout(termId);
};

/**
 * Pass abtest_impression event to GTM
 */

const abtest_impression: PipelineHandler = () => {
  sendAbTestImpression();
};

/**
 * Return to the registration page to allow the user to change
 * their account during checkout screen
 */

const change_piano_account: PipelineHandler = () => {
  changePianoAccount();
};

/**
 * Store abandoned term in localStorage for later use in homepage popups
 */

interface SetAbandonedTermRequest {
  id: string;
}

const set_abandoned_term: PipelineHandler<SetAbandonedTermRequest> = ({
  id,
}) => {
  if (!window.localStorage || !window.sessionStorage) {
    return;
  }

  const items: [string, Storage, string][] = [
    ['abandoned_cart.id', window.localStorage, id],
    // eslint-disable-next-line rulesdir/prefer-use-date
    ['abandoned_cart.timestamp', window.localStorage, Date.now().toString()],
    ['abandoned_cart.set_this_session', window.sessionStorage, 'true'],
  ];

  let failed = false;
  items.forEach(([key, storage, value]) => {
    try {
      if (id === null) {
        storage.removeItem(key);
      } else {
        storage.setItem(key, value);
      }
    } catch (e) {
      console.error(e);
      failed = true;
    }
  });

  if (failed) {
    items.forEach(([key, storage]) => {
      storage.removeItem(key);
    });
  }
};

/**
 * Trigger function on element within page
 */

interface TriggerInteractionRequest {
  funcName: string;
  selector: string;
}

const trigger_interaction: PipelineHandler<TriggerInteractionRequest> = ({
  funcName,
  selector,
}) => {
  const ele = document.querySelector(selector) as unknown as Record<
    string,
    () => void
  >;
  if (ele !== null && ele[funcName] && typeof ele[funcName] === 'function') {
    ele[funcName]();
  }
};

/**
 * Save the currently selected term
 */

interface SaveSelectedTermRequest {
  termId: string;
}

const save_selected_term: PipelineHandler<SaveSelectedTermRequest> = ({
  termId,
}) => {
  window.localStorage.setItem('selected_term', termId);
};

/**
 * Return saved term
 */

interface GetSelectedTermResponse {
  termId: string | null;
}

const get_selected_term: PipelineHandler<unknown, GetSelectedTermResponse> = (
  _payload,
  send,
) => {
  const termId = window.localStorage.getItem('selected_term');
  send({
    termId,
  });
};

/**
 * Return whether custom Print User address is available
 */

interface GetCustomUserAddress {
  hasUserAddress: boolean;
}

const get_custom_user_address: PipelineHandler<
  unknown,
  GetCustomUserAddress
> = (_payload, send) => {
  const store = window.getStore();
  const pianoFeature = store.getState().features.piano;
  const { pathname } = window.location;
  if (
    !pianoFeature.enabled ||
    !pianoFeature.data.supportPrintBundle ||
    pathname !== '/my-account/'
  ) {
    return;
  }

  onPianoReady((tp) => {
    tp.pianoId.loadExtendedUser({
      extendedUserLoaded: (
        data: PianoExtendedUserCallbackData | Record<string, never>,
      ) => {
        if (data.custom_field_values) {
          const customFieldValues = data.custom_field_values;
          const arrIndex = Object.values(customFieldValues).findIndex(
            (arr) => arr.field_name === 'billing_address',
          );
          if (arrIndex !== -1) {
            if (customFieldValues[arrIndex].value !== '') {
              send({
                hasUserAddress: true,
              });
            }
          }
        }
      },
      formName: 'print-bundle',
    });
  });
};

/**
 * Return whether beta page exists (i.e. if site has beta)
 */

interface GetHasBetaResponse {
  hasBeta: boolean;
  isBeta: boolean;
}

const get_has_beta: PipelineHandler<unknown, GetHasBetaResponse> = (
  _payload,
  send,
) => {
  const store = window.getStore();
  const { hasBeta } = store.getState().conf;
  send({
    hasBeta,
    isBeta: true,
  });
};

interface GetEnterpriseDetailsResponse {
  enterpriseMyAccountLogoUrl: string | undefined;
}

const get_enterprise_details: PipelineHandler<
  unknown,
  GetEnterpriseDetailsResponse
> = (_payload, send) => {
  const store = window.getStore();
  const { staticUrl } = store.getState().settings;
  const { domain } = store.getState().conf;
  const pianoFeature = store.getState().features.piano;
  const { memberOrigin } = store.getState().piano;

  if (
    pianoFeature.enabled &&
    pianoFeature.data.enterpriseSubscriptions &&
    memberOrigin
  ) {
    const enterprise = pianoFeature.data.enterpriseSubscriptions.find(
      (subscription) =>
        subscription.name.toLowerCase() === memberOrigin.toLowerCase(),
    );
    const enterpriseMyAccountLogoUrl = enterprise?.myAccountLogoPath
      ? `https://${domain}${staticUrl}${enterprise.myAccountLogoPath}`
      : '';
    send({
      enterpriseMyAccountLogoUrl,
    });
  }
};

interface GetMemberOriginResponse {
  memberOrigin: string | undefined;
}

const get_member_origin: PipelineHandler<unknown, GetMemberOriginResponse> = (
  _payload,
  send,
) => {
  const store = window.getStore();
  const { memberOrigin } = store.getState().piano;
  send({
    memberOrigin,
  });
};

interface GetRegistrationOnlyResponse {
  registrationOnly: boolean;
}

const get_registration_only: PipelineHandler<
  unknown,
  GetRegistrationOnlyResponse
> = (_payload, send) => {
  const store = window.getStore();
  const { piano } = store.getState().features;
  send({
    registrationOnly: piano.enabled && piano.data.registrationOnly,
  });
};

interface GetSupportNewsletterLandingPageResponse {
  supportNewsletterLandingPage: boolean;
}

const get_support_newsletter_landing_page: PipelineHandler<
  unknown,
  GetSupportNewsletterLandingPageResponse
> = (_payload, send) => {
  const store = window.getStore();
  const { mail } = store.getState().features;
  send({
    supportNewsletterLandingPage:
      mail.enabled && mail.data.supportNewslettersLandingPage,
  });
};

interface SetTermsRequest {
  offerId: string;
  terms: PianoTerm[];
  trackingId?: string;
}

const set_terms: PipelineHandler<SetTermsRequest> = (payload) => {
  const store = window.getStore();
  store.dispatch(pianoSlice.actions.setOfferId(payload.offerId));
  store.dispatch(
    pianoSlice.actions.setTerms(
      payload.terms.map((term) => simplifyPianoTerm(term)),
    ),
  );
  setPianoTrackingId(payload.trackingId);
};

const pianoPipelineHandlers: Record<string, PipelineHandler> = {
  abtest_impression,
  begin_checkout,
  change_piano_account,
  get_custom_user_address,
  get_enterprise_details,
  get_has_beta,
  get_hash,
  get_member_origin,
  get_registration_only,
  get_selected_term,
  get_support_newsletter_landing_page,
  get_suzuka,
  gtm_add_to_cart,
  gtm_datalayer,
  gtm_event,
  gtm_gift_impression,
  gtm_promo_applied,
  gtm_remove_from_cart,
  gtm_set_commercial_checkout,
  gtm_set_commercial_purchase,
  gtm_set_impressions,
  gtm_set_template,
  has_rendered_paywall,
  has_visited_login,
  logout,
  open_mail_form,
  redirect_complete_profile,
  redirect_to_homepage,
  redirect_to_manage_seats,
  redirect_to_newsletters,
  redirect_to_register,
  register_newsletter,
  save_selected_term,
  set_abandoned_term,
  set_cookie,
  set_modal_details,
  set_terms,
  trigger_interaction,
};

const templatePianoPipelineHandlers: Record<string, PipelineHandler> = {};

export function addTemplatePipelineHandler(
  key: string,
  handler: PipelineHandler,
) {
  templatePianoPipelineHandlers[key] = handler;
}

interface PipelineRequest {
  eventName: string;
  id: string;
  payload: unknown;
  pipeline: true;
}

function handlePipeline(event: MessageEvent, data: PipelineRequest) {
  const handler =
    templatePianoPipelineHandlers[data.eventName] ||
    pianoPipelineHandlers[data.eventName];
  if (handler) {
    handler(data.payload, (payload) => {
      const message = {
        eventName: `${data.eventName}_response`,
        id: data.id,
        payload,
        pipeline: true,
      };

      if (event.source) {
        (event.source as WindowProxy).postMessage(
          JSON.stringify(message),
          event.origin,
        );
      }
    });
  } else {
    console.error(`Unexpected eventName: ${data.eventName}`);
  }
}

export function setupPipeline(): void {
  window.addEventListener('message', (event) => {
    if (!pianoApiOrigins.includes(event.origin)) {
      return;
    }

    if (typeof event.data === 'string') {
      try {
        const data = JSON.parse(event.data) as PipelineRequest;
        if (data?.pipeline) {
          handlePipeline(event, data);
        }
      } catch (e) {
        console.error(e);
      }
    }
  });
}
