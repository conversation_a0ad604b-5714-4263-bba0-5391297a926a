/**
 * User & Login/Registration
 */
export interface PianoUserData {
  aud: string;
  email: string;
  email_confirmation_required: boolean;
  exp: number;
  family_name: string;
  given_name: string;
  iat: number;
  iss: string;
  jti: string;
  login_timestamp: string;
  passwordType?: string;
  sub: string;
}

export interface PianoLoginUserData extends PianoUserData {
  confirmed: boolean;
  firstName: string;
  lastName: string;
  uid: string;
  valid: boolean;
}

interface PianoRegistrationUserData extends PianoUserData {
  passwordType: string;
  r: boolean;
  rememberMe: boolean;
}

interface PianoCustomFieldResponse {
  name?: string;
  value?: string;
}

export interface PianoUserProfileData {
  create_date?: number;
  custom_fields?: PianoCustomFieldResponse[];
  email?: string;
  firstName?: string;
  lastName?: string;
  password?: string;
  reset_password_email_sent?: boolean;
  uid?: string;
}

interface PianoCustomFormData {
  created: number;
  email_creator: string;
  field_definition_id: number;
  field_name: string;
  identity_id: number | null;
  sort_order: number;
  value: string;
}

export interface PianoExtendedUserCallbackData {
  aid: string;
  changed_email: boolean;
  custom_field_values: PianoCustomFormData[];
  email: string;
  exp: number;
  first_name: string;
  has_all_custom_field_values_filled: boolean;
  last_name: string;
  need_resend_confirmation_email: boolean;
  passwordless: boolean;
  uid: string;
  updated: number;
}

interface PianoLoadExtendedUser {
  extendedUserLoaded?: (data: PianoExtendedUserCallbackData) => void;
  formName?: string;
}

/**
 * Subscriptions & Terms
 */

interface PianoConversionEvent {
  chargeAmount: number;
  chargeCurrency: string;
  cookie_domain: string;
  email: string;
  expires: string;
  promotionId: string | null;
  rid: string;
  startedAt: string;
  termConversionId: string;
  termId: string;
  token_list: string;
  uid: string;
  user_token: string;
}

export interface PianoBillingPlan {
  billing: string;
  billingInfo: string;
  billingPeriod: string;
  currency: string;
  cycles: string;
  date: string;
  duration: string;
  isFree: string;
  isFreeTrial: string;
  isPayWhatYouWant: string;
  isTrial: string;
  originalPrice?: string;
  originalPriceValue: number | null;
  period: string;
  price: string;
  priceAndTax: number;
  priceChargedStr: string;
  priceValue: number;
  pricelessBillingPost?: string;
  pricelessBillingPre?: string;
  shortPeriod: string;
  totalBilling: string;
}

export interface PianoTerm {
  billingPlanTable: PianoBillingPlan[];
  chargeAmount: string;
  chargeCurrency: string;
  description: string;
  displayLine: string;
  isFreeTrial: string;
  isSubscription: boolean;
  name: string;
  sharedAccountCount?: number;
  termId: string;
  type: 'dynamic' | 'payment';
  verificationAmount: number;
}

interface PianoSubmitPaymentEvent {
  offerId: string;
  term: PianoTerm;
}

/**
 * API
 */

interface PianoApiResponse<T> {
  code: number;
  conversions: T[];
  count: number;
  limit: number;
  offset: number;
  total: number;
  ts: number;
}

interface PianoApiResource {
  aid: string;
  description: string;
  image_url: string;
  name: string;
  rid: string;
}

interface PianoApiTerm {
  aid: string;
  create_date: number;
  description: string;
  name: string;
  resource: PianoApiResource;
  term_id: string;
  type: string;
}

interface PianoApiUser {
  create_date: number;
  email: string;
  first_name: string;
  last_name: string;
  personal_name: string;
  uid: string;
}

interface PianoApiAccess {
  access_id: string;
  can_revoke_access: boolean;
  expire_date: number;
  granted: boolean;
  parent_access_id: string;
  resource: PianoApiResource;
  start_date: number;
  user: PianoApiUser;
}

export interface PianoApiConversion {
  aid: string;
  create_date: number;
  term: PianoApiTerm;
  term_conversion_id: string;
  type: string;
  user_access: PianoApiAccess;
}

export interface PianoSocialLogin {
  uri: string;
}

export interface PianoSocialAuthResponse {
  access_token?: string;
  apple_confirmation_available?: boolean;
  code?: string;
  email?: string;
  facebook_confirmation_available?: boolean;
  first_name?: string;
  google_confirmation_available?: boolean;
  last_name?: string;
  linked_in_confirmation_available?: boolean;
  linking_state?: string;
  password_confirmation_available?: boolean;
  preauth_token?: string;
  redirect_uri: string;
  refresh_token?: string;
  social_type?: string;
  status: string;
  twitter_confirmation_available?: boolean;
}

/**
 * Carts & Offers
 */

export interface PianoTrackingItem {
  brand: string;
  category: string;
  id: string;
  list: string;
  name: string;
  price: number;
  trial?: string;
  variant: string;
}

export interface PianoTrackingImpression extends PianoTrackingItem {
  position: number;
}

export interface PianoTrackingProduct extends PianoTrackingItem {
  quantity: number;
}

/**
 * Configuration
 */

export interface PianoLoginCallbackData {
  source?: 'PIANOID' | 'OFFER' | undefined;
  stage: undefined;
  token: string;
  user: PianoLoginUserData;
}

export interface PianoIdParameters {
  confirmation?: undefined | 'none';
  containerSelector?: string;
  disableSignUp?: boolean;
  displayMode?: string;
  height?: string;
  iframeUrl?: string;
  langChange?: (...args: unknown[]) => void;
  loggedIn?: (data: PianoLoginCallbackData) => void;
  loggedOut?: () => void;
  loginDisplayed?: (...args: unknown[]) => void;
  loginFailed?: (...args: unknown[]) => void;
  loginSuccess?: (...args: unknown[]) => void;
  profileUpdate?: (...args: unknown[]) => void;
  registerDisplayed?: (...args: unknown[]) => void;
  registrationFailed?: (...args: unknown[]) => void;
  registrationSuccess?: (...args: unknown[]) => void;
  resetPasswordToken?: string;
  screen?: string;
  width?: string;
}

interface PianoEmptyModalDetails {
  state: 'registered' | 'subscribe' | 'visitPromotion' | 'register';
}

interface PianoShowModalDetails {
  eventLabel: string;
  eventName: string;
  state: 'show';
}

export type PianoModalDetails = PianoEmptyModalDetails | PianoShowModalDetails;

export interface PianoMeterData {
  maxViews: number;
  meterName: string;
  totalViews: number;
  views: number;
  viewsLeft: number;
}

type PianoMeterCallback = (event: PianoMeterData) => void;

export interface LoginSuccessEvent {
  event: 'loginSuccess';
  params: PianoLoginUserData;
  registration: boolean;
  source: 'PIANOID' | 'OFFER';
  stage: undefined;
  user_token: string;
}

export interface RegistrationSuccessEvent {
  cookie_domain: string;
  extendExpiredAccessEnabled: boolean;
  registration: boolean;
  source: 'PIANOID' | 'OFFER';
  token: string;
  user: PianoRegistrationUserData;
}

export interface CheckoutCloseEvent {
  state:
    | 'checkoutCompleted'
    | 'alreadyHasAccess'
    | 'voucherRedemptionCompleted'
    | 'close';
}

export interface CheckoutCustomEvent {
  eventName: string;
  params?: unknown;
}

export type PianoHandler =
  | ['checkoutClose', (event: CheckoutCloseEvent) => void]
  | ['checkoutComplete', (conversion: PianoConversionEvent) => void]
  | ['checkoutCustomEvent', (event: CheckoutCustomEvent) => void]
  | ['loginRequired', () => void]
  | ['loginSuccess', (data: LoginSuccessEvent) => void]
  | ['logout', () => void]
  | ['meterActive', PianoMeterCallback]
  | ['meterExpired', PianoMeterCallback]
  | ['registrationSuccess', (data: RegistrationSuccessEvent) => void]
  | ['startCheckout', () => void]
  | ['submitPayment', (data: PianoSubmitPaymentEvent) => void];

export type PianoHandlerKey = PianoHandler[0];

export type PianoHandlerCallback<K extends PianoHandlerKey> = {
  [H in PianoHandler as H[0]]: H[1];
}[K];

export type PianoPushAction =
  | ['addHandler', ...PianoHandler]
  | ['init', () => void]
  | ['setAid', string]
  | ['setContentAuthor', string]
  | ['setContentCreated', string]
  | ['setContentSection', string]
  | ['setCustomVariable', string, boolean | string]
  | ['setCxenseSiteId', string]
  | ['setDebug', boolean]
  | ['setEndpoint', string]
  | ['setFbPixelId', string]
  | ['setPageURL', string]
  | ['setPianoIdUrl', string]
  | ['setSandbox', boolean]
  | ['setTags', string[]]
  | ['setUsePianoIdUserProvider', boolean]
  | ['setUseTinypassAccounts', boolean]
  | ['setZone', string]
  | [
      'setGA4Config',
      {
        eventParameters: {
          page_location: string;
          page_title: string;
          send_page_view: boolean;
          user_id: string | undefined;
        };
        measurementId: string;
      },
    ];

export type PianoPushActionsInterface = PianoPushAction[];

export type PianoTinypassInterface = PianoPushActionsInterface & {
  aid: string;
  api: {
    callApi: <T>(
      url: string,
      body: Record<string, unknown>,
      callback: (response: PianoApiResponse<T>) => void,
    ) => void;
  };
  customVariables: { string: string | boolean } | undefined;
  experience: {
    init: () => void;
  };
  hasVisitedLogin?: boolean;
  isInitialized: boolean;
  modalDetails?: {
    modalName: string;
    modalParams: PianoModalDetails;
  };
  myaccount: {
    show(params: {
      containerSelector: string;
      displayMode: string;
      width: string;
    }): void;
  };
  offer: {
    closeInline: (value: string) => void;
    show: (params: {
      displayMode?: string;
      offerId: string;
      templateId?: string;
      termId?: string;
    }) => void;
    startCheckout: (props: {
      offerId: string;
      termId: string;
      trackingId?: string;
    }) => void;
    startRedeemVoucher: () => void;
  };
  pianoId: {
    getToken: () => null | string;
    getUser: () => PianoLoginUserData | null;
    init: (options: PianoIdParameters) => void;
    isUserValid: () => boolean;
    loadExtendedUser: (options: PianoLoadExtendedUser) => void;
    loginByToken: (token: string) => Promise<void>;
    logout: () => Promise<void>;
    show: (options: PianoIdParameters) => void;
  };
  sandbox: boolean;
  setGA4Config: (props: {
    eventParameters?: {
      page_location?: string;
      page_title?: string;
      send_page_view?: boolean;
      user_id?: string;
    };
    measurementId: string;
  }) => void;
  tags: string | undefined;
};

declare global {
  interface Window {
    afterPaywallKept: () => void;
    afterPaywallRemoved: () => void;
    checkCorporateAccess: () => void;
    checkSharedSubscriptionReminder: () => void;
    enableFeaturesBehindPaywall: () => void;
    setHasAccess: (value: boolean) => void;
    setHasDPEAccess: (value: boolean) => void;
    setHasPuzzlesAccess: (value: boolean) => void;
    setHasSubscription: (value: boolean) => void;
    setMemberOrigin: (memberOrigin: string) => void;
    setMemberRole: (memberRole: string) => void;
    setPianoABTestingVariant: (variant: string) => void;
    setSubscribeButtonHref: (href: string) => void;
    setSubscribeButtonOverride: (override: boolean) => void;
    setSubscribeButtonStyle: (style: string) => void;
    setSubscribeButtonText: (text: string) => void;
    showCompleteProfileReminder: () => void;
    tp?: PianoPushActionsInterface | PianoTinypassInterface;
    tracking?: {
      cart?: {
        paymentInfo: string;
        products: PianoTrackingProduct[];
      };
      impressions?: PianoTrackingImpression[];
      itemImpressions?: string[];
    };
    validatePayments: () => void;
  }
}

/**
 * Related types & definitions
 */

export interface OriginalPublicationResponse {
  message?: string;
  success: boolean;
}

export enum PianoSubscriberType {
  VISITOR = 'visitor',
  MEMBER = 'member',
  SUBSCRIBER = 'subscriber',
  ENTERPRISE = 'enterprise',
}

export interface MemberTypeData {
  memberID: string;
  memberOrigin?: string | undefined;
  memberRole?: string | undefined;
  memberType: PianoSubscriberType;
}

export const MEMBER_TYPE_DATA = 'memberTypeData';

export type PianoReadyCallback = (
  tp: PianoTinypassInterface,
) => void | Promise<void>;

export enum SimplePeriodUnit {
  DAYS,
  WEEKS,
  MONTHS,
  YEARS,
}

export interface SimplePeriod {
  /**
   * The numerical value of the period e.g. `4` in `4 weeks`.
   */
  factor: number;
  /**
   * The unit of time e.g. `week` in `4 weeks`.
   */
  unit: SimplePeriodUnit;
}

export type SimpleBillingPlanDiscount =
  | {
      /**
       * If `true` a discount has been applied. `false` otherwise.
       * Usually occurs due to application of a promo code.
       */
      hasDiscount: false;
    }
  | {
      /**
       * The reduction in price due to the discount.
       */
      discount: number;
      /**
       * If `true` a discount has been applied. `false` otherwise.
       * Usually occurs due to application of a promo code.
       */
      hasDiscount: true;
      /**
       * The original price of this plan.
       */
      originalPrice: number;
    };

type SimpleBaseBillingPlan = {
  calculate: {
    /**
     * Helper function to calculate the savings from the `relative` plan
     * compared to this one. Equivalent to `calculateSavings` with the
     * first argument bound to this plan.
     * @param base The base (original) plan to compare to.
     * Only executed when both this `term` and the relative plan/`term`
     * are not `undefined`.
     * @returns The difference in price as a number.
     */
    savings(base: SimpleBillingPlan | SimpleTerm): number;
    savings(
      base: SimpleBillingPlan | SimpleTerm | undefined,
    ): number | undefined;

    /**
     * Helper function to calculate the savings as a percentage from
     * the `relative` plan compared to this one. Equivalent to
     * `calculateSavingsPercentage` with the first argument bound to this plan.
     * @param base The base (original) plan to compare to.
     * Only executed when both this `term` and the relative plan/`term`
     * are not `undefined`.
     * @returns The difference in price as percentage fraction (e.g. `0`..`1`).
     */
    savingsPercentage(base: SimpleBillingPlan | SimpleTerm): number;
    savingsPercentage(
      base: SimpleBillingPlan | SimpleTerm | undefined,
    ): number | undefined;

    /**
     * Helper function to calculate the savings as a percentage from
     * the `relative` plan compared to this one as a string. Equivalent to
     * `formatSavingsPercentage` with the first argument bound to this plan.
     * @param base The base (original) plan to compare to.
     * Only executed when both this `term` and the relative plan/`term`
     * are not `undefined`.
     * @returns The difference in price as percentage (e.g. `'0'`..`'100'`).
     */
    savingsPercentageString(base: SimpleBillingPlan | SimpleTerm): string;
    savingsPercentageString(
      base: SimpleBillingPlan | SimpleTerm | undefined,
    ): string | undefined;

    /**
     * Helper function to calculate the savings from the `relative` plan
     * compared to this one. Equivalent to `calculateSavings` with the
     * first argument bound to this plan.
     * @param base The base (original) plan to compare to.
     * @param truncateUnnecessaryDecimals Truncate unnecessary decimals
     * if true (e.g. `'120.00'` -> `'120'`).
     * Only executed when both this `term` and the relative plan/`term`
     * are not `undefined`.
     * @returns The difference in price as a string.
     */
    savingsString(
      base: SimpleBillingPlan | SimpleTerm,
      truncateUnnecessaryDecimals?: boolean,
    ): string;
    savingsString(
      base: SimpleBillingPlan | SimpleTerm | undefined,
      truncateUnnecessaryDecimals?: boolean,
    ): string | undefined;
  };
  /**
   * Helper function to format info from the given billing plan for
   * use in templates. Equivalent to `formatSimplePlan` with the
   * first two arguments bound to this plan and its term.
   * @param callback A callback to process the formatted billing data.
   * Only executed when `term` is not `undefined`.
   * @param options optional additional configurations.
   * @returns The result of the callback.
   */
  format<R>(
    callback: (info: SimpleFormatValues, term: SimpleTerm) => R,
    options?: SimpleFormatOptions,
  ): R;
  /**
   * The period that payments occur.
   */
  period: SimplePeriod;
  /**
   * The cost of one payment.
   */
  price: number;
} & SimpleBillingPlanDiscount;

export type SimpleFixedBillingPlan = SimpleBaseBillingPlan & {
  /**
   * The duration of access. This can represent a single or multiple
   * payment periods.
   */
  duration: SimplePeriod;
  /**
   * The count of durations that will be repeated before moving to the
   * next step of the billing plan table.
   */
  durationCycles: number;
  /**
   * If `true` this plan will repeat indefinitely. `false` otherwise.
   */
  endless: false;
  /**
   * The count of payments that will be repeated before moving to the next step
   * of the billing plan table. Important to note that this is equal to the
   * number of payments per duration multiplied by the number of duration
   * cycles. This is done so that a duration of 4 weeks that repeats 3 times is
   * equivalent to a duration that is 12 weeks with no repeats. Periods per
   * duration isn't being exposed intentionally, as the behaviour that it could
   * be used for is better supported by this field.
   */
  periodCycles: number;
};

export type SimpleEndlessBillingPlan = SimpleBaseBillingPlan & {
  /**
   * If `true` this plan will repeat indefinitely. `false` otherwise.
   */
  endless: true;
};

export type SimpleBillingPlan =
  | SimpleFixedBillingPlan
  | SimpleEndlessBillingPlan;

// For sites with no split, treat users as premium.
export enum SimpleResourceType {
  BASIC,
  PREMIUM,
}

export interface SimpleResource {
  type: SimpleResourceType;
}

export interface SimpleTerm {
  /**
   * The last payment plan in the billing configuration. For terms with
   * multiple plans, this will represent the last plan. For terms with
   * only one plan, this will be equivalent to the first plan.
   */
  finalPlan: SimpleBillingPlan;
  /**
   * The first payment plan in the billing configuration. For terms with
   * multiple plans, this will repesent the first plan. For terms with
   * only one plan, this will be equivalent to the final plan.
   */
  firstPlan: SimpleBillingPlan;
  /**
   * `true` if this plan has multiple plans. Otherwise, `false`.
   */
  hasMultiplePlans: boolean;
  /**
   * Term name
   */
  name: string;
  /**
   * All provided plans in the billing configuration.
   */
  plans: SimpleBillingPlan[];
  /**
   * Resource associated with the given term.
   */
  resource: SimpleResource;
  /**
   * Number of additional shared seats that the subscription comes with
   */
  sharedAccountCount: number;
  /**
   * Unique identifier for this term.
   */
  termId: string;
  /**
   * The type of term.
   */
  type: 'dynamic' | 'payment';
}

export type SimpleDiscountedFormatValues =
  | {
      discount?: never;
      /**
       * If `true` a discount has been applied. `false` otherwise.
       * Usually occurs due to application of a promo code.
       */
      hasDiscount: false;
      originalPrice?: never;
    }
  | {
      /**
       * The price reduction that has been applied due to the discount.
       * Listens to the `truncateUnnecessaryDecimals` option.\
       * true: `'9.95'`, `'120'`.\
       * false: `'9.95'`, `'120.00'`.
       */
      discount: string;
      /**
       * If `true` a discount has been applied. `false` otherwise.
       * Usually occurs due to application of a promo code.
       */
      hasDiscount: true;
      /**
       * The original price of this plan.
       * Listens to the `truncateUnnecessaryDecimals` option.\
       * true: `'9.95'`, `'120'`.\
       * false: `'9.95'`, `'120.00'`.
       */
      originalPrice: string;
    };

export type SimpleSharedFormatValues = SimpleDiscountedFormatValues & {
  /**
   * `'4 weeks'`, `'month'`, `'2 years'`
   */
  period: string;

  /**
   * `'4 weeks'` -> `4`\
   * `'month'` -> `1`\
   * `'2 years'` -> `2`
   */
  periodFactor: number;

  /**
   * `'4-weekly'`, `'monthly'`, `'2-yearly'`
   */
  periodFrequency: string;

  /**
   * `'4 weeks'` -> `SimplePeriodUnit.WEEKS`\
   * `'month'` -> `SimplePeriodUnit.MONTHS`\
   * `'2 years'` -> `SimplePeriodUnit.YEARS`
   */
  periodUnit: SimplePeriodUnit;

  /**
   * The plan used.
   */
  plan: SimpleBillingPlan;

  /**
   * Listens to the `truncateUnnecessaryDecimals` option.\
   * true: `'9.95'`, `'120'`.\
   * false: `'9.95'`, `'120.00'`.
   */
  price: string;
};

export type SimpleFixedFormatValues = SimpleSharedFormatValues & {
  /**
   * `'4 weeks'`, `'month'`, `'2 years'`.\
   * Duration will include for both cycles and
   * payment repeats within an access period.
   */
  duration: string;

  /**
   * `'4 weeks'` -> `4`\
   * `'month'` -> `1`\
   * `'2 years'` -> `2`\
   * Duration will include for both cycles and
   * payment repeats within an access period.
   */
  durationFactor: number;

  /**
   * `'4-weekly'`, `'monthly'`, `'2-yearly'`\
   * Duration will include for both cycles and
   * payment repeats within an access period.
   */
  durationFrequency: string;

  /**
   * `'4 weeks'` -> `SimplePeriodUnit.WEEKS`\
   * `'month'` -> `SimplePeriodUnit.MONTHS`\
   * `'2 years'` -> `SimplePeriodUnit.YEARS`\
   * Duration will include for both cycles and
   * payment repeats within an access period.
   */
  durationUnit: SimplePeriodUnit;

  /**
   * If `true` the plan will repeat indefinitely.
   */
  endless: false;
};

export type SimpleEndlessFormatValues = SimpleSharedFormatValues & {
  duration?: never;
  durationFactor?: never;
  durationFrequency?: never;
  durationUnit?: never;
  /**
   * If `true` the plan will repeat indefinitely.
   */
  endless: true;
};

export type SimpleFormatValues =
  | SimpleFixedFormatValues
  | SimpleEndlessFormatValues;

export interface SimpleFormatOptions {
  /**
   * Converts the factor of the period to 1, adjusting
   * price to match. A plan with period `'4 weeks'` would be
   * converted to a plan with period `'1 week'`.
   * A plan with period `'1 week` would be unaffected by this flag.
   * Duration is unaffected by this flag.\
   * Default: `false`
   */
  convertPeriodToSingleFactor?: boolean;

  /**
   * Converts the unit of the period to a given unit, adjusting
   * price and factor to match. A plan with period `'1 year'` would be
   * converted to a plan with period `'52 weeks'`.
   * Duration is unaffected by this flag.\
   * Default: `false`
   */
  convertPeriodToUnit?: SimplePeriodUnit;

  /**
   * Trim a trailing `'.00'` from prices.\
   * Default: `false`
   */
  truncateUnnecessaryDecimals?: boolean;
}
