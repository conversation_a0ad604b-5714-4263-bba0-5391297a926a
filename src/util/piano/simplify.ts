import { SimplePeriodUnit, SimpleResourceType } from 'types/Piano';

import {
  formatSavings,
  formatSavingsPercentage,
  formatSimplePlan,
} from './format';
import {
  calculatePeriodRatio,
  calculateSavings,
  calculateSavingsPercentage,
} from './math';

import type {
  PianoBillingPlan,
  PianoTerm,
  SimpleBillingPlan,
  SimpleBillingPlanDiscount,
  SimplePeriod,
  SimpleTerm,
} from 'types/Piano';

export function simplifyPeriod(period: string): SimplePeriod {
  const match =
    /^(every )?((\d+) )?(.*?)(\(?s\)?)?( .*)?$/g.exec(period) ?? [];
  const factorText = match[2] ?? '1';
  const unitText = match[4];
  if (!factorText || !unitText) {
    throw new Error(`Unknown period format: ${period}`);
  }

  const factor = parseInt(factorText, 10);
  let unit: SimplePeriodUnit;
  switch (unitText) {
    case 'day':
      unit = SimplePeriodUnit.DAYS;
      break;
    case 'week':
      unit = SimplePeriodUnit.WEEKS;
      break;
    case 'month':
      unit = SimplePeriodUnit.MONTHS;
      break;
    case 'year':
      unit = SimplePeriodUnit.YEARS;
      break;
    default:
      throw new Error(`Unknown period unit: ${unitText}`);
  }

  return {
    factor,
    unit,
  };
}

function simplifyBillingPlan(
  plan: PianoBillingPlan,
  dynamic: boolean,
): SimpleBillingPlan {
  // Old term config uses signed int max to represent endless cycles
  // New term config only provides an infinity symbol in the duration
  const endless =
    plan.cycles === '2147483647' || plan.duration.includes('\u221e');
  const period = simplifyPeriod(plan.billingPeriod);
  const price = plan.priceValue;
  const originalPrice = plan.originalPriceValue;
  const hasOriginalPrice =
    originalPrice !== undefined && originalPrice !== null;
  const discount = hasOriginalPrice ? originalPrice - price : undefined;
  const discountData: SimpleBillingPlanDiscount =
    hasOriginalPrice && discount !== undefined
      ? {
          discount,
          hasDiscount: true,
          originalPrice,
        }
      : {
          hasDiscount: false,
        };

  if (endless) {
    return {
      calculate: {
        savings: () => 0,
        savingsPercentage: () => 0,
        savingsPercentageString: () => '',
        savingsString: () => '',
      },
      endless,
      format: <R>() => ({}) as R,
      period,
      price,
      ...discountData,
    };
  }
  const duration = simplifyPeriod(plan.duration.split(',')[0]);
  const repeats = Math.ceil(calculatePeriodRatio(duration, period));
  // +1 to count the initial cycle for dynamic plans
  const cycles = parseInt(plan.cycles, 10) + (dynamic ? 1 : 0);
  return {
    calculate: {
      savings: () => 0,
      savingsPercentage: () => 0,
      savingsPercentageString: () => '',
      savingsString: () => '',
    },
    duration,
    durationCycles: cycles,
    endless,
    format: <R>() => ({}) as R,
    period,
    periodCycles: cycles * repeats,
    price,
    ...discountData,
  };
}

/**
 * Converts a Piano Term to a Simple Term for use in templates.
 * @param term The term to convert.
 * @returns An equivalent Simple Term.
 */
export function simplifyPianoTerm(term: PianoTerm): SimpleTerm {
  const plans = term.billingPlanTable.map((plan) =>
    simplifyBillingPlan(plan, term.type === 'dynamic'),
  );
  const resourceType = term.name.toLowerCase().includes('basic')
    ? SimpleResourceType.BASIC
    : SimpleResourceType.PREMIUM;
  const simpleTerm: SimpleTerm = {
    finalPlan: plans[plans.length - 1],
    firstPlan: plans[0],
    hasMultiplePlans: plans.length > 1,
    name: term.name,
    plans,
    resource: {
      type: resourceType,
    },
    sharedAccountCount: term.sharedAccountCount ?? 0,
    termId: term.termId,
    type: term.type,
  };

  plans.forEach((plan) => {
    /* eslint-disable no-param-reassign */
    plan.format = (callback, options) =>
      formatSimplePlan(simpleTerm, plan, callback, options);
    plan.calculate.savings = (base) => {
      if (!base) {
        return undefined as unknown as number;
      }
      return calculateSavings(base, plan);
    };
    plan.calculate.savingsPercentage = (base) => {
      if (!base) {
        return undefined as unknown as number;
      }
      return calculateSavingsPercentage(base, plan);
    };
    plan.calculate.savingsPercentageString = (base) => {
      if (!base) {
        return undefined as unknown as string;
      }
      return formatSavingsPercentage(base, plan);
    };
    plan.calculate.savingsString = (base, truncateUnnecessaryDecimals) => {
      if (!base) {
        return undefined as unknown as string;
      }
      return formatSavings(base, plan, truncateUnnecessaryDecimals);
    };
    /* eslint-enable no-param-reassign */
  });

  return simpleTerm;
}
